<?php
class Auto_import_json extends CI_Controller
{
    public $_code_mail = [
        '0'     => ['msg' => 'Erro ao salvar o arquivo ZIP.', 'tipo' => 'Error'],
        '1'     => ['msg' => 'Erro: O arquivo ZIP não foi baixado corretamente.', 'tipo' => 'Error'],
        '2'     => ['msg' => 'Erro ao abrir o arquivo zip. Detalhes: %s', 'tipo' => 'Error'],
        '3'     => ['msg' => 'Erro ao extrair o arquivo.', 'tipo' => 'Error'],
        '4'     => ['msg' => 'Erro ao importar informações NCM. - Tabela de Apoio.', 'tipo' => 'Error'],
        '5'     => ['msg' => '(Grave) Erro ao importar NCM Atributo.', 'tipo' => 'Error'],
        '6'     => ['msg' => 'Arquivo extraído com sucesso.', 'tipo' => 'Sucesso'],
        '7'     => ['msg' => 'A versão do arquivo zipado não é maior que a versão no txt.', 'tipo' => 'Info'],
        '8'     => ['msg' => 'Informações NCM importadas com sucesso.', 'tipo' => 'Sucesso'],
        '9'     => ['msg' => 'NCM Atributo importados com sucesso.', 'tipo' => 'Sucesso'],
        '10'    => ['msg' => 'Importado com Sucesso.', 'tipo' => 'Sucesso'],
    ];

    public $_error_zip = [
        ZipArchive::ER_OK          => "ER_OK: Sem erro",
        ZipArchive::ER_MULTIDISK   => "ER_MULTIDISK: Multi-disk zip não suportado",
        ZipArchive::ER_RENAME      => "ER_RENAME: Erro ao renomear arquivo temporário",
        ZipArchive::ER_CLOSE       => "ER_CLOSE: Erro ao fechar arquivo",
        ZipArchive::ER_SEEK        => "ER_SEEK: Erro de seek",
        ZipArchive::ER_READ        => "ER_READ: Erro de leitura",
        ZipArchive::ER_WRITE       => "ER_WRITE: Erro de escrita",
        ZipArchive::ER_CRC         => "ER_CRC: CRC incorreto",
        ZipArchive::ER_ZIPCLOSED   => "ER_ZIPCLOSED: Arquivo zip fechado",
        ZipArchive::ER_NOENT       => "ER_NOENT: Arquivo não encontrado",
        ZipArchive::ER_EXISTS      => "ER_EXISTS: Arquivo já existe",
        ZipArchive::ER_OPEN        => "ER_OPEN: Não foi possível abrir o arquivo",
        ZipArchive::ER_TMPOPEN     => "ER_TMPOPEN: Não foi possível criar arquivo temporário",
        ZipArchive::ER_ZLIB        => "ER_ZLIB: Erro na Zlib",
        ZipArchive::ER_MEMORY      => "ER_MEMORY: Falha de alocação de memória",
        ZipArchive::ER_CHANGED     => "ER_CHANGED: Entrada modificada",
        ZipArchive::ER_COMPNOTSUPP => "ER_COMPNOTSUPP: Método de compressão não suportado",
        ZipArchive::ER_EOF         => "ER_EOF: Fim de arquivo inesperado",
        ZipArchive::ER_INVAL       => "ER_INVAL: Argumento inválido",
        ZipArchive::ER_NOZIP       => "ER_NOZIP: Não é um arquivo zip",
        ZipArchive::ER_INTERNAL    => "ER_INTERNAL: Erro interno",
        ZipArchive::ER_INCONS      => "ER_INCONS: Zip inconsistente",
        ZipArchive::ER_REMOVE      => "ER_REMOVE: Não foi possível remover o arquivo",
        ZipArchive::ER_DELETED     => "ER_DELETED: Entrada foi excluída",
   ];

    public $_send_mail = [];
    public $_virtual_table = [];
    public $_attrs = [];
    public $_processar_empresa = [];
    public $_attr_estrutura_completa = [];
    public $_notifica_pucomex = [];
    public function __construct()
    {
        parent::__construct();
        $this->CI =& get_instance();
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('ctr_grupo_pergunta_model');
        $this->load->model("ctr_resposta_model");


        $this->load->model('ncm_atributo_model');
        $this->load->model('cad_item_model');
        $this->load->model([
            "catalogo/produto_model",
        ]);

        set_time_limit(0);

        ini_set('memory_limit', '4096M');
        ini_set('memory_limit','-1');
        ini_set('display_errors', 'on');
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
    }

    public function index()
    {
           echo 'iniciando 16 - 09 - 2025 v1';
        $this->db->truncate('ncm_atributo_temp');

        $this->db->query("INSERT INTO ncm_atributo_temp (
            id, ncm, codigo, codigo_pai, nome_apresentacao, forma_preenchimento,
            definicao, tamanho_maximo, orientacao_preenchimento, modalidade,
            obrigatorio, data_inicio_vigencia, data_fim_vigencia,
            dominio, objetivos, orgaos, atributo_condicionante,
            condicionados, multivalorado
        )
        SELECT 
            id, ncm, codigo, codigo_pai, nome_apresentacao, forma_preenchimento,
            definicao, tamanho_maximo, orientacao_preenchimento, modalidade,
            obrigatorio, data_inicio_vigencia, data_fim_vigencia,
            dominio, objetivos, orgaos, atributo_condicionante,
            condicionados, multivalorado     
        FROM ncm_atributo;
        ");

        $this->import();

        if (!empty($this->_send_mail)) {
            $this->send_mail($this->_send_mail);
        }

        $this->atualizaCampos();
        
     $this->atualiza_status_preenchimento();
    }


    public function atualiza_status_preenchimento()
    {
        echo 'iniciando';
        $database_name = $this->db->database;
        echo $database_name;
        $trigger_name = 'tr_item_update_catalogo_envio';
        $table_name = 'item';
        $trigger_exists = false;
        $query = $this->db->query("
            SELECT TRIGGER_NAME 
            FROM INFORMATION_SCHEMA.TRIGGERS 
            WHERE TRIGGER_NAME = '$trigger_name' 
            AND EVENT_OBJECT_TABLE = '$table_name' 
            AND TRIGGER_SCHEMA = DATABASE()
        ");
        
        // Cloud não possui essa trigger por isso nao vamos adicionar
        if ($query->num_rows() > 0 && $database_name != 'gestaotarifaria') {
            $trigger_exists = true;
            $this->db->query("DROP TRIGGER IF EXISTS tr_item_update_catalogo_envio;");
        }

        $empresas = $this->get_entries();

        foreach ($empresas as $empresa)
        {
            echo ' Empresa : '.$empresa->razao_social.PHP_EOL;
            $this->db->query("set sql_mode='';");
            $this->db->select("(SELECT 
                        COUNT(*)
                    FROM
                        comex cm
                    WHERE
                        ind_ecomex = 'EI'
                            AND cm.part_number_original = i.part_number
                            AND cm.unidade_negocio = i.estabelecimento
                            AND cm.id_empresa = i.id_empresa
                    LIMIT 1) AS tipo_item,
                ci.part_number,
                ci.estabelecimento,
                ci.id_empresa,
                CASE
                    WHEN
                        EXISTS( SELECT 
                                1
                            FROM
                                ncm_atributo n
                            WHERE
                                n.ncm = ci.ncm_proposto
                                    AND (n.codigo = 'null'))
                    THEN
                        '5'
                    WHEN
                        COUNT(*) = 0
                            OR COUNT(CASE
                            WHEN attr.codigo IS NULL OR attr.codigo = '' THEN 1
                        END) = COUNT(*)
                    THEN
                        '1'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.obrigatorio = 1
                                    AND (attr.codigo IS NULL OR attr.codigo = '')
                            THEN
                                1
                        END) > 0
                    THEN
                        '2'
                    WHEN
                        COUNT(CASE
                            WHEN
                                (attr.obrigatorio = 0
                                    AND (attr.codigo IS NULL OR attr.codigo = ''))
                                    OR (attr.id_item IS NULL)
                            THEN
                                1
                            ELSE NULL
                        END) > 0
                    THEN
                        '3'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.codigo IS NOT NULL
                                    AND attr.codigo <> ''
                            THEN
                                1
                        END) = COUNT(*)
                    THEN
                        '4'
                    ELSE '0'
                END AS status_preenchimento", false);  
    
            $this->db->from('cad_item ci');
            $this->db->join('cad_item_attr attr', 'ci.id_item = attr.id_item', 'left');
            $this->db->join('item i', 'ci.part_number = i.part_number
            AND ci.estabelecimento = i.estabelecimento
            AND ci.id_empresa = i.id_empresa', 'inner');
 
            $this->db->where('ci.id_empresa', $empresa->id_empresa);
            $this->db->group_by('ci.id_item');
    
            $query = $this->db->get();
   
            $count = 0;
            $total = $query->num_rows();
            if ($query->num_rows() > 0)
            {
                while ($item = $query->unbuffered_row()) {
                    echo $count.'/'.$total.PHP_EOL;
                    $count++;
        
                    $this->db->query("UPDATE item 
                    SET status_attr = '{$item->status_preenchimento}'
                    WHERE part_number = '{$item->part_number}' 
                    AND estabelecimento = '{$item->estabelecimento}'
                    AND id_empresa = '{$item->id_empresa}'
                    AND status_attr <> '{$item->status_preenchimento}'");
    
                    if ($item->tipo_item == 0)
                    {
                        if ($item->status_preenchimento == 3 || $item->status_preenchimento == 4)
                        {
                            $this->db->query(" UPDATE item i
                            SET i.wf_status_atributos = '7'
                                WHERE i.part_number = '{$item->part_number}' 
                                AND i.estabelecimento = '{$item->estabelecimento}'
                                AND i.id_empresa = '{$item->id_empresa}'");  

                                echo ' Item Nacional: '.$item->part_number.' '.$this->db->last_query().PHP_EOL;
                        }
                    } 
    
                }
    
            }
        }

        if ($trigger_exists == true && $database_name != 'gestaotarifaria') {
 
            $this->db->query("CREATE DEFINER=`caue_sampaio`@`%` TRIGGER tr_item_update_catalogo_envio
                AFTER UPDATE ON item
                FOR EACH ROW
                BEGIN
                    DECLARE v_id_item BIGINT(20) DEFAULT NULL;
                
                    IF ((NEW.descricao_proposta_completa <> ''
                            OR (OLD.descricao_proposta_completa != NEW.descricao_proposta_completa
                                OR OLD.descricao_proposta_completa IS NULL AND NEW.descricao_proposta_completa IS NOT NULL))
                        OR (NEW.id_status = 2)
                        OR (NEW.wf_status_atributos = 7)) THEN
                        SELECT
                            cad_item.id_item INTO v_id_item
                        FROM cad_item
                        WHERE 1=1
                            AND cad_item.part_number = NEW.part_number
                            AND cad_item.id_empresa = NEW.id_empresa
                            AND cad_item.estabelecimento = NEW.estabelecimento;
                        IF v_id_item IS NOT NULL THEN
                            CALL sp_cria_catalogo_envio(v_id_item);
                        END IF;
                    END IF;
                END;
                        ");
        }

        echo 'fim';
    }

    public function atualiza_obrigatorios($ncm)
    {       
        $this->db->select('attr.id');
        $this->db->from('cad_item_attr attr');
        $this->db->join('cad_item ci', 'ci.id_item = attr.id_item', 'inner');
        $this->db->where('attr.ativo', '1');

        if (!empty($ncm))
        {
            $this->db->where("ci.ncm_proposto", $ncm);
        }

        $this->db->where("attr.obrigatorio <> (
            SELECT n.obrigatorio 
            FROM ncm_atributo n 
            WHERE n.codigo = attr.atributo 
            AND ci.ncm_proposto = n.ncm 
            LIMIT 1
        )", null, false);
        $this->db->group_by('attr.id');

        $db = $this->db->get();


        if ($db->num_rows()) {
            foreach ($db->result() as $item)
            {
                echo 'Atualizando obrigatorio'.$item->id.PHP_EOL;
                
                $this->db->query(" UPDATE cad_item_attr attr
                INNER JOIN ncm_atributo n ON n.codigo = attr.atributo
                INNER JOIN cad_item ci ON ci.id_item = attr.id_item
                SET attr.obrigatorio = n.obrigatorio
                WHERE attr.atributo = n.codigo
                and n.ncm = ci.ncm_proposto
                and attr.obrigatorio <> n.obrigatorio
                and attr.id = '{$item->id}'");
            }

        }

    }

    public function atualizaCampos()
    {
      //  $ncms = $this->db->query("SELECT ncm FROM ncm_atributo group by ncm;");
        // foreach ($ncms->result() as $ncm) {

        $mudancas =  $this->db->query("select sub.ncm, sub.coluna_diferente from ( 
            SELECT 
                            COALESCE(a.ncm, b.ncm) AS ncm,
                            CASE 
                                WHEN a.codigo <> b.codigo OR (a.codigo IS NULL XOR b.codigo IS NULL) THEN 'codigo'
                                WHEN a.codigo_pai <> b.codigo_pai OR (a.codigo_pai IS NULL XOR b.codigo_pai IS NULL) THEN 'codigo_pai'
                                WHEN a.obrigatorio <> b.obrigatorio OR (a.obrigatorio IS NULL XOR b.obrigatorio IS NULL) THEN 'obrigatorio'
                                WHEN a.data_inicio_vigencia <> b.data_inicio_vigencia OR (a.data_inicio_vigencia IS NULL XOR b.data_inicio_vigencia IS NULL) THEN 'data_inicio_vigencia'
                                WHEN a.data_fim_vigencia <> b.data_fim_vigencia OR (a.data_fim_vigencia IS NULL XOR b.data_fim_vigencia IS NULL) THEN 'data_fim_vigencia'
                                WHEN a.atributo_condicionante <> b.atributo_condicionante OR (a.atributo_condicionante IS NULL XOR b.atributo_condicionante IS NULL) THEN 'atributo_condicionante'
                                WHEN a.condicionados <> b.condicionados OR (a.condicionados IS NULL XOR b.condicionados IS NULL) THEN 'condicionados'
                                WHEN a.multivalorado <> b.multivalorado OR (a.multivalorado IS NULL XOR b.multivalorado IS NULL) THEN 'multivalorado'
                                ELSE 'Sem diferença'
                            END AS coluna_diferente,
                            a.codigo AS codigo_a, b.codigo AS codigo_b,
                            a.codigo_pai AS codigo_pai_a, b.codigo_pai AS codigo_pai_b,
                            a.obrigatorio AS obrigatorio_a, b.obrigatorio AS obrigatorio_b,
                            a.data_inicio_vigencia AS data_inicio_vigencia_a, b.data_inicio_vigencia AS data_inicio_vigencia_b,
                            a.data_fim_vigencia AS data_fim_vigencia_a, b.data_fim_vigencia AS data_fim_vigencia_b,
                            a.atributo_condicionante AS atributo_condicionante_a, b.atributo_condicionante AS atributo_condicionante_b,
                            a.condicionados AS condicionados_a, b.condicionados AS condicionados_b
                        FROM 
                            ncm_atributo b
                        LEFT JOIN 
                            ncm_atributo_temp a ON a.ncm = b.ncm AND a.codigo = b.codigo
                        WHERE 
                                (
                            a.codigo <> b.codigo 
                            OR a.codigo_pai <> b.codigo_pai
                            OR a.obrigatorio <> b.obrigatorio
                            OR a.data_inicio_vigencia <> b.data_inicio_vigencia
                            OR a.data_fim_vigencia <> b.data_fim_vigencia
                            OR a.atributo_condicionante <> b.atributo_condicionante
                            OR a.condicionados <> b.condicionados
                            OR a.multivalorado <> b.multivalorado
                            OR (a.codigo IS NULL XOR b.codigo IS NULL)
                            OR (a.codigo_pai IS NULL XOR b.codigo_pai IS NULL)
                            OR (a.obrigatorio IS NULL XOR b.obrigatorio IS NULL)
                            OR (a.data_inicio_vigencia IS NULL XOR b.data_inicio_vigencia IS NULL)
                            OR (a.data_fim_vigencia IS NULL XOR b.data_fim_vigencia IS NULL)
                            OR (a.atributo_condicionante IS NULL XOR b.atributo_condicionante IS NULL)
                            OR (a.condicionados IS NULL XOR b.condicionados IS NULL)
                            OR (a.multivalorado IS NULL XOR b.multivalorado IS NULL))
                        
                        UNION ALL
                        
                        SELECT 
                            COALESCE(a.ncm, b.ncm) AS ncm,
                            CASE 
                                WHEN a.codigo <> b.codigo OR (a.codigo IS NULL XOR b.codigo IS NULL) THEN 'codigo'
                                WHEN a.codigo_pai <> b.codigo_pai OR (a.codigo_pai IS NULL XOR b.codigo_pai IS NULL) THEN 'codigo_pai'
                                WHEN a.obrigatorio <> b.obrigatorio OR (a.obrigatorio IS NULL XOR b.obrigatorio IS NULL) THEN 'obrigatorio'
                                WHEN a.data_inicio_vigencia <> b.data_inicio_vigencia OR (a.data_inicio_vigencia IS NULL XOR b.data_inicio_vigencia IS NULL) THEN 'data_inicio_vigencia'
                                WHEN a.data_fim_vigencia <> b.data_fim_vigencia OR (a.data_fim_vigencia IS NULL XOR b.data_fim_vigencia IS NULL) THEN 'data_fim_vigencia'
                                WHEN a.atributo_condicionante <> b.atributo_condicionante OR (a.atributo_condicionante IS NULL XOR b.atributo_condicionante IS NULL) THEN 'atributo_condicionante'
                                WHEN a.condicionados <> b.condicionados OR (a.condicionados IS NULL XOR b.condicionados IS NULL) THEN 'condicionados'
                                WHEN a.multivalorado <> b.multivalorado OR (a.multivalorado IS NULL XOR b.multivalorado IS NULL) THEN 'multivalorado'
                                ELSE 'Sem diferença'
                            END AS coluna_diferente,
                            a.codigo AS codigo_a, b.codigo AS codigo_b,
                            a.codigo_pai AS codigo_pai_a, b.codigo_pai AS codigo_pai_b,
                            a.obrigatorio AS obrigatorio_a, b.obrigatorio AS obrigatorio_b,
                            a.data_inicio_vigencia AS data_inicio_vigencia_a, b.data_inicio_vigencia AS data_inicio_vigencia_b,
                            a.data_fim_vigencia AS data_fim_vigencia_a, b.data_fim_vigencia AS data_fim_vigencia_b,
                            a.atributo_condicionante AS atributo_condicionante_a, b.atributo_condicionante AS atributo_condicionante_b,
                            a.condicionados AS condicionados_a, b.condicionados AS condicionados_b
                        FROM 
                            ncm_atributo_temp a
                        LEFT JOIN 
                            ncm_atributo b ON a.ncm = b.ncm AND a.codigo = b.codigo
                        WHERE 
                            b.ncm IS NULL) as sub
                            
                            group by sub.ncm, sub.coluna_diferente
                            ORDER BY sub.ncm, sub.coluna_diferente ");

        if ($mudancas->num_rows() > 0) {
            $ncms_alteradas = [];
            foreach ($mudancas->result() as $ncm) {
                    // if ($ncm->ncm < '39269090')
                    // {
                    //     continue;
                    // }
                    echo $ncm->ncm.' qtd ncms: '.$mudancas->num_rows().' '.$this->memory_mb(memory_get_usage()).PHP_EOL;

                    $alteracoes = $this->get_diff($ncm->ncm);
 
                    if (empty($alteracoes['attrs_diff']) && empty($alteracoes['attrs_removidos'])) {
                        continue;
                    }
                    
                    $ncms_alteradas[$ncm->ncm] = [
                        'ncm' => $ncm->ncm,
                        'atributos_alterados' => $alteracoes['attrs_diff'],
                        'atributos_removidos' => $alteracoes['attrs_removidos'],
                    ];
                    
               $this->limpar_atributos_nao_relacionados($ncm->ncm);

               $this->atualiza_obrigatorios($ncm->ncm);
            }

            $this->envio_relatorios_revisao_pucomex($ncms_alteradas);
        }

    }
    public function get_attr_pai($ncm)
    {
        $codigos = [];
        foreach ($ncm as $item) {
            if (isset($item->codigo)) {
                $codigos[] = $item->codigo;
            }
        }
        
        return $codigos;
        
    }

    public function get_condicionados($ncm)
    {
        $codigos = [];

             foreach ($ncm as $row) { 
                $json =  $row->condicionados;
             
                if (empty($json)) {
                    continue;
                }
                $json = trim($json);
     
                $dados = json_decode($json, true);

                if (is_array($dados)) {
                    foreach ($dados as $item) {
                        if (isset($item['atributo']['codigo'])) {
                            $codigos[] = $item['atributo']['codigo'].' (Condicionados)';
                        }
                    }
                } else {
                    echo "Erro ao decodificar JSON: " . json_last_error_msg();
                }
            }
     
        return $codigos;
    }

   public function buscarAtributoRecursivo($dados, $atributo, $attPai = null) {
        foreach ($dados as $attAtual => $info) {
            // Verifica se o atributo atual é o que estamos buscando
            if ($attAtual === $atributo) {
                return [
                    'filho' => $attPai !== null,
                    'att_pai' => $attPai,
                    'ativacao' => isset($info['valores_ativacao'][0]) ? $info['valores_ativacao'][0] : null
                ];
            }
    
            // Se houver filhos, chama recursivamente
            if (isset($info['filhos']) && is_array($info['filhos']) && !empty($info['filhos'])) {
                $resultado = $this->buscarAtributoRecursivo($info['filhos'], $atributo, $attAtual);
                if ($resultado !== null) {
                    return $resultado;
                }
            }
        }
    
        // Se não encontrou
        return null;
    }
    
    public function buscarCodigoPorAtributo($array, $atributoBuscado) {
        foreach ($array as $objeto) {
            if (isset($objeto->atributo) && $objeto->atributo === $atributoBuscado) {
                return $objeto->codigo;
            }
        }
        return null; // Retorna nulo se não encontrar
    }

    public function get_part_number_revisao_pucomex($ncm, $attrs_diff, $attrs_removidos, $arvoreCompleta = null)
    {
        $this->load->model(array('cad_item_wf_atributo_model', 'log_wf_atributos_model'));


        $this->db->order_by("ci.id_empresa", "ASC");  
        $this->db->join('item i', 'i.part_number = ci.part_number AND i.estabelecimento = ci.estabelecimento AND i.id_empresa = ci.id_empresa ', 'inner');                    
       // $this->db->join('comex cx', 'cx.part_number_original = ci.part_number AND cx.unidade_negocio = ci.estabelecimento AND cx.id_empresa = ci.id_empresa', 'inner');                    

      //  $this->db->where('cx.ind_ecomex', 'EI'); 

        $this->db->where('ci.ncm_proposto', $ncm);    
        //----------------------------------------------------------
        // $this->db->where('i.id_empresa', 1);
         //$this->db->where('i.wf_status_atributos <> 6', null, false);
        $this->db->where('i.wf_status_atributos', 7);
        //----------------------------------------------------------
        $data =   $this->db->get("cad_item ci");
        $itens = $data->result();

        if (empty($itens)) {
            return;
        }
        // $registro_log = $this->log_wf_atributos_model->registrar_log_cron('CRON - Envio tabela temporária', sess_user_id(),'Itens Importados');

        // aqui
        foreach ($itens as $item) {

            if (!empty($arvoreCompleta))
            {
                $valor_cad_item_attr = $this->get_attr($item->id_item);
                $houve_alteracao = true;
 
                if (empty($valor_cad_item_attr))
                {
                    continue;
                }
                
                foreach ($valor_cad_item_attr as $att)
                {
                 
                    $res = $this->buscarAtributoRecursivo( $arvoreCompleta, $att->atributo);
                    if ($res['filho'] == true)
                    {
                        $houve_alteracao = false;
                        $valor_pai_salvo = $this->buscarCodigoPorAtributo($valor_cad_item_attr, $res['att_pai']);
                        $val = $this->formatarCondicao($res['ativacao']);
 

                        $cond = $this->parseCondicao($res['ativacao']);
                        if ($cond) {
                            $operador = $cond['operador'];
                            $valor    = $cond['valor'];
                        
                            // garante que NULL vire algo válido no eval
                            $lhs = is_null($valor_pai_salvo) ? "null" : var_export($valor_pai_salvo, true);
                        
                         //   var_dump($lhs, $operador, $valor);
                        
                            $cond_res = eval("return $lhs $operador $valor;");
                        } else {
                            $lhs = is_null($valor_pai_salvo) ? "null" : var_export($valor_pai_salvo, true);
                            $cond_res = eval("return $lhs $val;");
                        }
                        
                        if ($cond_res && $houve_alteracao == false)
                        {
                            $houve_alteracao = true;
                        }
    
                    }
                }
    
                if ($houve_alteracao == false)  
                {
                    continue;
                }
            }
         
            $this->_notifica_pucomex['dados'][$item->id_empresa][] = [
           
                    'part_number' => $item->part_number,
                    'estabelecimento' => $item->estabelecimento,
                    'id_empresa' => $item->id_empresa,
                    'ncm_proposto' => $item->ncm_proposto,
                    'descricao_mercado_local' => $item->descricao_mercado_local,
                    'atributos' => $attrs_diff,
                    'atributos_removidos' => $attrs_removidos,
                    'status_atributos' => $item->wf_status_atributos,
                    'status_fiscal' => $item->id_status,
                    'status_preenchimento' => $item->status_attr,
            ];
 
            $descAttrs = '';
            foreach ( $attrs_diff as $attrs)
            {
                if (empty($descAttrs)) {
                    $descAttrs = 'Atributos criados: ';
                }
                $descAttrs .= '<b>'.$attrs . '</b>, ';
            }
            if (strlen($descAttrs) >= 2) {  
                $descAttrs = substr($descAttrs, 0, -2);
            } 
            $this->register_status_log(6, 'Em revisão por alteração no PUCOMEX', 1, 'Atualização da NCM', "part_number = '{$item->part_number}' AND estabelecimento = '{$item->estabelecimento}' AND id_empresa = {$item->id_empresa}  AND wf_status_atributos = 7 ",$item, $descAttrs);
        }
    } 

    public function parseCondicao($val) {
        if (preg_match("/(==|!=)\s*(.+)/", $val, $matches)) {
            return [
                'operador' => $matches[1],
                'valor'    => "'" . trim($matches[2]) . "'"
            ];
        }
        return null;
    }

    public function formatarCondicao($val) {
        // Expressão regular para pegar o operador e o valor
        if (preg_match("/(==|!=)\s*(.+)/", $val, $matches)) {
            $operador = $matches[1];
            $valor = trim($matches[2]);
    
            // Adiciona aspas simples ao redor do valor
            return $operador . " '" . $valor . "'";
        }
        return $val;
    }
    public function register_status_log($status_novo, $justificativa, $id_usuario, $tipo, $where, $item, $descAttrs)
    {
        if (empty($where)) {
            return false;
        }

        if (empty($item->part_number))
        {
            return false;
        }
        if (empty($item->estabelecimento))
        {
            return false;
        }
        if (empty($item->id_empresa))
        {
            return false;
        }

        if (empty($status_novo)) {
            return false;
        }

        if (empty($justificativa)) {
            return false;
        }

        if (empty($id_usuario)) {
            return false;
        }

        if (empty($tipo)) {
            return false;
        }            

        $this->db->query("INSERT INTO log_wf_atributos
            (
                part_number, 
                estabelecimento, 
                id_empresa,
                status_atual, 
                justificativa, 
                informacoes_integracao, 
                detalhes,
                id_usuario,
                tipo,
                criado_em,
                nao_atualiza_item
            )SELECT 
                i.part_number,
                i.estabelecimento, 
                i.id_empresa,
                (SELECT status FROM status_wf_atributos WHERE id = '{$status_novo}') as status_atual,
                '{$justificativa}', 
                NULL,
                '{$descAttrs}',
                '{$id_usuario}',
                '{$tipo}',
                NOW(),
                1
            FROM item i
            WHERE {$where} ");

        $this->db->query("UPDATE item
                        SET wf_status_atributos = '{$status_novo}',
                        data_modificacao = NOW()
                        WHERE {$where}");

        return true;
    }

    /**
     * Função auxiliar final que extrai os valores de ativação, operadores de comparação
     * e os operadores lógicos que os unem.
     *
     * @param array $blocoCondicao O array representando a condição.
     * @return array Uma lista altamente detalhada das condições (ex: ['== 06', '|| == 05']).
     */
    public function extrairValoresDeAtivacao(array $blocoCondicao): array
    {
        $valoresFormatados = [];
        $blocoAtual = $blocoCondicao;
        $operadorLogicoPendente = null;
    
        // Loop para percorrer a cadeia de condições aninhadas.
        while ($blocoAtual !== null) {
            $operadorComparacao = $blocoAtual['operador'] ?? null;
            $valor = $blocoAtual['valor'] ?? null;
            
            // Continua apenas se tiver os dados essenciais.
            if ($operadorComparacao !== null && $valor !== null) {
                $prefixoLogico = '';
                // Se houver um operador lógico da iteração anterior, prepara o prefixo.
                if ($operadorLogicoPendente !== null) {
                    $prefixoLogico = ' ' . $operadorLogicoPendente . ' ';
                }
    
                // Formata a string de condição conforme o formato solicitado.
                $stringCondicao = $prefixoLogico . $operadorComparacao . ' ' . $valor;
                $valoresFormatados[] = $stringCondicao;
            }
    
            // Guarda o operador de composição para a próxima iteração e avança para a próxima condição.
            $operadorLogicoPendente = $blocoAtual['composicao'] ?? null;
            $blocoAtual = $blocoAtual['condicao'] ?? null;
        }
    
        return $valoresFormatados;
    }
    
    
    public function construirArvoreDeAtributos(array $nodesArray): array
    {
        $arvoreNesteNivel = [];
    
        foreach ($nodesArray as $node) {
            if (isset($node['atributo']['codigo']) && isset($node['condicao'])) {
                $codigoAtual = $node['atributo']['codigo'];
                $valoresAtivacao = $this->extrairValoresDeAtivacao($node['condicao']);
                
                $filhosDoNodeAtual = [];
    
                // VERIFICAÇÃO RECURSIVA CORRIGIDA
                if (isset($node['atributo']['condicionados']) && is_array($node['atributo']['condicionados']) && !empty($node['atributo']['condicionados'])) {
                    
                    $subNodesArray = $node['atributo']['condicionados'];
                    
                    // A chamada recursiva continua a mesma.
                    $filhosDoNodeAtual = $this->construirArvoreDeAtributos($subNodesArray);
                }
    
                // Monta a estrutura final para este atributo, incluindo a chave 'filhos'.
                $arvoreNesteNivel[$codigoAtual] = [
                    'valores_ativacao' => $valoresAtivacao,
                    'filhos' => $filhosDoNodeAtual,
                ];
            }
        }
    
        return $arvoreNesteNivel;
    }
        
    /**
     * Nova função principal que mapeia TODOS os atributos de um NCM,
     * e constrói a árvore de filhos para cada um, SE ELA EXISTIR.
     *
     * @param array $arrayDeAtributos O array de objetos de atributo para um NCM.
     * @return array Uma lista completa de todos os atributos e suas hierarquias.
     */
    public function mapearAtributosCompletos(array $arrayDeAtributos): array
    {
        $resultadoMapeado = [];

        // Itera sobre CADA atributo fornecido no array.
        foreach ($arrayDeAtributos as $atributo) {
            if (!is_object($atributo) || !isset($atributo->codigo)) {
                continue; // Pula itens malformados
            }

            $codigoAtual = $atributo->codigo;
            $arvoreDeFilhos = [];

            // Verifica se este atributo tem filhos para iniciar a construção da árvore.
            // A lógica de construção da árvore (a função recursiva) só é chamada se necessário.
            if (isset($atributo->condicionados) && !empty($atributo->condicionados)) {
                $primeiroNivelFilhos = [];
                // Adicionado um 'is_string' para segurança, caso o dado já venha como array.
                if(is_string($atributo->condicionados)) {
                    $primeiroNivelFilhos = json_decode($atributo->condicionados, true);
                } else if (is_array($atributo->condicionados)) {
                    $primeiroNivelFilhos = $atributo->condicionados;
                }

                if (is_array($primeiroNivelFilhos) && !empty($primeiroNivelFilhos)) {
                    // Chama a função recursiva (da resposta anterior) para construir a árvore.
                    $arvoreDeFilhos = $this->construirArvoreDeAtributos($primeiroNivelFilhos);
                }
            }
            
            // Adiciona o atributo atual ao resultado, junto com outros dados úteis.
            // A chave 'filhos' conterá a árvore completa ou um array vazio.
            $resultadoMapeado[$codigoAtual] = [
                'nome_apresentacao' => $atributo->nome_apresentacao ?? 'N/A', // Fonte 14, 22, 30...
                'obrigatorio' => $atributo->obrigatorio ?? 'N/A', // Fonte 15, 23, 32...
                'orgaos' => $atributo->orgaos ?? 'N/A', // Fonte 16, 25, 33...
                'filhos' => $arvoreDeFilhos
            ];
        }

        return $resultadoMapeado;
    }


    public function get_diff($ncm)
    {
        $this->db->where('ncm', $ncm);
        $data =   $this->db->get("ncm_atributo");
        $ncm_atual = $data->result();

        $condicionados_atual = $this->get_condicionados($ncm_atual);
        $codigo_pai_atual = $this->get_attr_pai($ncm_atual);

        $this->db->where('ncm', $ncm);
        $data =   $this->db->get("ncm_atributo_temp");
        $ncm_anterior = $data->result();

        $condicionados_anterior = $this->get_condicionados($ncm_anterior);
        $codigo_pai_anterior = $this->get_attr_pai($ncm_anterior);
        
        $condicionados_atual = array_unique($condicionados_atual);
        $condicionados_anterior = array_unique($condicionados_anterior);

        $codigo_pai_atual = array_unique($codigo_pai_atual);
        $codigo_pai_anterior = array_unique($codigo_pai_anterior);

        sort($condicionados_atual);
        sort($condicionados_anterior);

        sort($codigo_pai_atual);
        sort($codigo_pai_anterior);
 
        // Verifica se são iguais
        if ($condicionados_atual != $condicionados_anterior || $codigo_pai_atual != $codigo_pai_anterior)  {
           // echo "Os arrays são diferentes.\n";

            // Diferenças: o que tem em um que não tem no outro
            $attrs_diff = array_diff($condicionados_atual, $condicionados_anterior);
            $attrs_removidos = array_diff($condicionados_anterior, $condicionados_atual);

            $attr_pai_diff = array_diff($codigo_pai_atual, $codigo_pai_anterior);
            $attr_pai_removidos = array_diff($codigo_pai_anterior, $codigo_pai_atual);
 
            //   echo "Códigos que existem em tabela_1 mas não em tabela_2 atual :\n";
            if (!empty($attrs_diff) || !empty($attr_pai_diff)  ) 
            {
                $attrs_diff = array_merge($attrs_diff, $attr_pai_diff);
                $attrs_removidos = array_merge($attrs_removidos, $attr_pai_removidos);

                $arvoreCompleta = $this->mapearAtributosCompletos($ncm_atual);
                
                if (!empty($arvoreCompleta))
                {
                    foreach ($arvoreCompleta as $k =>  $att)
                    {
                        if (empty($att['filhos']))
                        {
                            if (!in_array($k, $attr_pai_diff))
                            {
                                unset($arvoreCompleta[$k]);
                            }
                        } else {
                            foreach ($att['filhos'] as $f1 => $filhos1)
                            {
                                if (empty($filhos1['filhos']))
                                {
                                    if (!in_array($f1, $attr_pai_diff) && !in_array($f1, $attrs_diff))
                                    {
                                         unset($arvoreCompleta[$k][$f1]);
                                    }
                                } else {
    
                                    foreach ($filhos1['filhos'] as $f2 => $filhos2)
                                    {
    
                                        if (empty($filhos2['filhos']))
                                        {
                                            if (!in_array($f2, $attr_pai_diff) && !in_array($f2, $attrs_diff))
                                            {
                                                 unset($arvoreCompleta[$k][$f1][$f2]);
                                            }
                                        }
                                    }
    
                                }
    
                            }
    
                        }
                      
                    }

                }

                 $this->get_part_number_revisao_pucomex($ncm, $attrs_diff, $attrs_removidos, $arvoreCompleta);
         
                return [
                    'attrs_diff' => $attrs_diff,
                    'attrs_removidos' => $attrs_removidos,
                ];
            }
            return [];
         //   echo "Códigos que existem em tabela_2 mas não em tabela_1:\n";
        }
    }
 
    public function get_entries()
    {
        $this->db->where('ativo', '1');
        //$this->db->where('id_empresa', '1');
       //  $this->db->where('id_empresa > 492', null,false); // aqui
        $query = $this->db->get('empresa');
        return $query->result();
    }

    public function envio_relatorios_revisao_pucomex($ncms_alteradas){

        $count_empresas = 0;
        $count_emails = 0;
 
        $empresas = $this->get_entries();

            $this->_notifica_pucomex['ncms'] = $ncms_alteradas;
            foreach ($empresas as $empresa) {

                $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
                if (!in_array('chk_destinatarios_revisao_pucomex', $funcoes_adicionais)) {
                    continue;
                }
                
                $this->_notifica_pucomex['dados'][$empresa->id_empresa]['empresa'] = [
                    'id_empresa' => $empresa->id_empresa,
                    'nome_fantasia' => $empresa->nome_fantasia,
                    'destinatarios_revisao_pucomex' => $empresa->destinatarios_revisao_pucomex
                ];

                $filename = '';
                if (!empty($this->_notifica_pucomex['dados'][$empresa->id_empresa][0])) {
                    $filename = $this->generate_xls_empresa($empresa->id_empresa);

                if($filename != ''){

                    $email_data['base_url'] = config_item('online_url');
                    $email_data['title'] = 'Revisão por alteração pucomex';
                    $email_data['url_download'] = config_item('online_url') . 'assets/tmp/'. $filename;
                    $body = $this->load->view('templates/notificacao_revisao_pucomex', $email_data, TRUE);

                    $emails = explode(';', $empresa->destinatarios_revisao_pucomex);

                    foreach($emails as $email){

                        $this->load->library('email');

                        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                        $this->email->to($email);
                        $this->email->subject('[Gestão Tarifária] - Revisão por alteracao pucomex');
                        $this->email->message($body);

                        $this->email->send();

                        $count_emails ++;
                    }
                }

                $count_empresas ++;

                 }
                
            }
 
        return array('empresas_relatorio' => $count_empresas, 'emails_enviados' => $count_emails);

    }

    public function generate_xls_empresa($id_empresa)
    {
        $status_preenchimento = [
            1 => 'Sem preenchimento',
            2 => 'Obrigatórios não preenchidos',
            3 => 'Opcionais não preenchidos',
            4 => 'Totalmente preenchidos',
            5 => 'Ncm sem atributos'
        ];

        $status_atributos = [
            1 => 'Item nacional',
            2 => 'Análise de atributos - Fiscal',
            3 => 'Preenchimento/Validação Engenharia',
            4 => 'Homologação da Classificação Fiscal',
            5 => 'Em revisão',
            6 => 'Em revisão por alteração no PUCOMEX',
            7 => 'Homologados'
        ];
        
        $status_fiscal = [
            1 => 'Pendente de Homologação',
            2 => 'Homologado',
            3 => 'Reprovado',
            4 => 'Inativo',
            5 => 'Em Revisão',
            6 => 'Em Análise',
            7 => 'Pendente de Informações',
            8 => 'Perguntas Respondidas',
            9 => 'Revisar Informações ERP',
            10 => 'Homologado em Revisão',
            11 => 'Revisar Informações Técnicas',
            12 => 'Informações ERP Revisadas',
            13 => 'Aguardando definição responsável',
            14 => 'Aguardando Descrição',
            15 => 'Perguntas Respondidas (Novas)'
        ];

       $sheet = array(
            'headers' => array(
                'Partnumber',
                'Descrição resumida',
                'Estabelecimento',
                'Empresa',
                'NCM impactada',
                'Status de preenchimento',
                'Status fiscal',
                'Status de atributos',
            )
        );

        foreach ($this->_notifica_pucomex['dados'] as $id_empresa_reg => $value) {
            
            if ($id_empresa_reg == $id_empresa) {
                echo 'processando empresa '.$id_empresa;
                $todosAtributos = [];
                $attrs_removidos = [];
                foreach ($this->_notifica_pucomex['ncms'] as $k => $ncm) {
                    // echo '---------';
                    // print_r($ncm);
                    
                    foreach ($ncm['atributos_alterados'] as $att) {
                        $todosAtributos[$k][] = $att;
                    }
                    foreach ($ncm['atributos_removidos'] as $att2) {
                        $attrs_removidos[$k][] = $att2;
                    }
                    // $ncms_alteradas[] = [
                    //     'ncm' => $ncm->ncm,
                    //     'atributos_alterados' => $alteracoes['attrs_diff'],
                    //     'atributos_removidos' => $alteracoes['attrs_removidos'],
                    // ];
                }              

                $maiorAddAttr = 0;
                $maiorRemovAttr = 0;
                if (!empty($todosAtributos))
                {
                    //$atributosUnicos = array_unique($todosAtributos);
                    $count_att = 0;
                    
                    foreach ($todosAtributos as $subArray) {
                        $countAtual = count($subArray);
                        if ($countAtual > $maiorAddAttr) {
                            $maiorAddAttr = $countAtual;
                        }
                    }

                    for ($i = 1; $i <= $maiorAddAttr; $i++) {
                        $sheet['headers'] = array_merge($sheet['headers'], array(
                            'Atributos Novos '.$i
                        ));
                    }

                }

                if (!empty($attrs_removidos))
                {
                   // $atributosRemovidos = array_unique($attrs_removidos);
                    $count_att = 0;
                    

                    foreach ($attrs_removidos as $subArray) {
                        $countAtual = count($subArray);
                        if ($countAtual > $maiorRemovAttr) {
                            $maiorRemovAttr = $countAtual;
                        }
                    }
                    for ($i = 1; $i <= $maiorRemovAttr; $i++) {
                        $sheet['headers'] = array_merge($sheet['headers'], array(
                            'Atributos Removidos '.$i
                        ));
                    }

                }
 
            }
        }
 
        $this->CI->load->library('PHPExcel');

        $excel = new PHPExcel();

        $excel->getActiveSheet()->getStyle('A1:H1')
            ->getAlignment()
            ->setWrapText(true);

        $excel->getActiveSheet()->fromArray($sheet['headers'], NULL, 'A1');
    
        $key = 0;
         $row = 1;
         foreach ($this->_notifica_pucomex['dados'][$id_empresa] as $item) {
            if (empty($item['ncm_proposto']))
            {
                continue;
            }
            $row++;
            $key++;
            $start_row = $row;
            $col = 0; // índice para as colunas (A, B, C...)

            // Define os dados a serem inseridos, na ordem das colunas
            // 'status_atributos' => $item->wf_status_atributos,
            // 'status_fiscal' => $item->id_status,
            // 'status_preenchimento' => $item->status_attr,

            $preenchimento = isset($status_preenchimento[$item['status_preenchimento']]) ? $status_preenchimento[$item['status_preenchimento']] : '';
            $fiscal = isset($status_fiscal[$item['status_fiscal']]) ? $status_fiscal[$item['status_fiscal']] : '';
            $atributos_st = isset($status_atributos[$item['status_atributos']]) ? $status_atributos[$item['status_atributos']] : '';

            $dados = [
                $item['part_number'],
                $item['descricao_mercado_local'],
                $item['estabelecimento'],
                $this->_notifica_pucomex['dados'][$id_empresa]['empresa']['nome_fantasia'],
                $item['ncm_proposto'],
                $preenchimento,
                $fiscal,
                $atributos_st

            ];

            // Insere os dados principais nas colunas A, B, C...
            foreach ($dados as $valor) {
                $colunaLetra = PHPExcel_Cell::stringFromColumnIndex($col); // Converte índice numérico em letra (ex: 0 => A)
                $excel->getActiveSheet()->getCell($colunaLetra . $row)->setValueExplicit($valor, PHPExcel_Cell_DataType::TYPE_STRING);
                $col++;
            }
        
            // Insere os atributos únicos após os dados principais
            if (!empty($todosAtributos))
            {
                foreach ($todosAtributos[$item['ncm_proposto']] as $att) {
                    $colunaLetra = PHPExcel_Cell::stringFromColumnIndex($col);
                    $excel->getActiveSheet()->getCell($colunaLetra . $row)->setValueExplicit($att, PHPExcel_Cell_DataType::TYPE_STRING);
                    $col++;
                }
            }

            if (!empty($atributosRemovidos))
            {
                foreach ($attrs_removidos[$item['ncm_proposto']] as $att) {
                    $colunaLetra = PHPExcel_Cell::stringFromColumnIndex($col);
                    $excel->getActiveSheet()->getCell($colunaLetra . $row)->setValueExplicit($att, PHPExcel_Cell_DataType::TYPE_STRING);
                    $col++;
                }  
            }
            
            // Cores alternadas
            $RowBgColor = ($key % 2 ? "DAE7F1" : "B8CCE4");
            $col = $col + $maiorAddAttr + $maiorRemovAttr;
            $colunaLetra = PHPExcel_Cell::stringFromColumnIndex($col);
            $excel->getActiveSheet()->getStyle('A'.$start_row.':'.$colunaLetra.$row)->getFill()->applyFromArray(array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'startcolor' => array(
                    'rgb' => $RowBgColor
                )
            ));

            // Bordas
            $excel->getActiveSheet()->getStyle('A'.$start_row.':'.$colunaLetra.$row)->applyFromArray(array(
            'borders' => array(
                    'allborders' => array('style' => PHPExcel_Style_Border::BORDER_THIN)
                )
            ));

            $excel->getActiveSheet()->getStyle('A'.$start_row.':'.$colunaLetra.$row)->applyFromArray(array(
            'borders' => array(
                    'top'    => array('style' => PHPExcel_Style_Border::BORDER_MEDIUM),
                    'bottom' => array('style' => PHPExcel_Style_Border::BORDER_MEDIUM)
                )
            ));

        }

        foreach (range('A', $colunaLetra) as $columnID)
        {
            $excel->getActiveSheet()->getColumnDimension($columnID)
                ->setAutoSize(true);
        }

        $excel->getActiveSheet()->getStyle('A1:'.$colunaLetra.'1')->applyFromArray(array(
            'fill' => array(
                'type' => PHPExcel_Style_Fill::FILL_SOLID,
                'color' => array('rgb' => '4F81BD'),
            ),
            'font' => array(
                'color' => array('rgb' => 'FFFFFF'),
                'bold' => true,
            ),
            'borders' => array(
                'bottom' => array(
                    'style' => PHPExcel_Style_Border::BORDER_THIN,
                    'color' => array('rgb' => '000000'),
                )
            ),
            'alignment' => array(
                'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical' => PHPExcel_Style_Alignment::VERTICAL_CENTER,
            ),
        ));

        $filename = 'relatorio-revisao-pucomex-empresa-'.$id_empresa.'-'.date('d-m-Y_H-i-s').'.xlsx';

        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');

        $excel->setActiveSheetIndex(0);

        $prop = $excel->getProperties();
        $prop->setCompany("Becomex");
        $prop->setCreator("Becomex");
        $prop->setLastModifiedBy("Becomex");
        $prop->setTitle("Documento");
        $prop->setSubject("Documento");
        $prop->setDescription("Becomex - Revisão pucomex");
        $prop->setKeywords("becomex revisão pucomex");

        $filepath = FCPATH . 'assets/tmp/' . $filename;

        $writer->save($filepath);

        return $filename;
    }

    public function get_cad_item($id_empresa, $ncm)
    {
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('ncm_proposto', $ncm);
        return  $this->db->get("cad_item");
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;


        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }

    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get('cad_item_attr');

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    private function filterUniqueAttributes($inputArray)
    {
        $attributeMap = [];

        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }

        $resultArray = array_values($attributeMap);

        usort($resultArray, function ($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });

        return $resultArray;
    }

    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);

        $arr_dbdata = $ncm_item["defaultAttrs"];
        $arr_attr   = $ncm_item["listaAtributos"];

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    public function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach ($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }
        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {

            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr;

            $attr_template["dbdata"] = ["codigo" => ""];

            foreach ($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                foreach ($attr_template["condicionados"] as &$cond) {

                    $index_atributo =  !empty($attr_template["dbdata"]['atributo']) ?
                        $attr_template["dbdata"]['atributo'] : null;
                    $index_atributo_codigo =  !empty($attr_template["dbdata"]['codigo']) ?
                        $attr_template["dbdata"]['codigo'] : null;

                    $index_atributo_pai =  !empty($cond['atributo']['codigo']) ?
                        $cond['atributo']['codigo'] : null;
                    $index_atributo_pai_codigo =  !empty($cond["condicao"]) ?
                        $cond["condicao"] : null;

                    $this->_attrs[] = [$index_atributo, $index_atributo_codigo, $index_atributo_pai, $index_atributo_pai_codigo];

                    if ($this->has_attr_cond($attr_template, $cond)) {
                        if (!empty($cond['atributo']["condicionados"])) {

                            $this->assoc_recursively($arr_dbdata, $cond['atributo']["condicionados"], $attr_template);
                        }

                        if (!empty($attr_template["dbdata"]['atributo'])) {
                            $this->_attrs['sim'][] = $attr_template["dbdata"]['atributo'];
                        }
                    }
                }

                if (!empty($this->_attrs['sim']) && in_array($attr_template['codigo'], $this->_attrs['sim'])) {
                    $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                }
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }

    public function filtrarAtributosFilhos($data)
    {
        $result = [];

        foreach ($data as $item) {

            if (!is_array($item) || count($item) < 4) {
                continue;
            }

            list($atributoPai, $valorPai, $atributoFilho, $condicoes) = $item;

            if ($valorPai == '' || is_null($valorPai)) {
                continue;
            }

            if (!is_array($condicoes) || !isset($condicoes['operador'], $condicoes['valor'])) {
                continue;
            }

            if ($this->avaliarCondicao($valorPai, $condicoes)) {
                $result[] = $atributoFilho;
            }
        }

        return array_unique($result);
    }

    public function avaliarCondicao($valorPai, $condicao)
    {
        $operador = $condicao['operador'];
        $valorCondicao = $condicao['valor'];

        if ($condicao['valor'] == 'true') {
            $valorCondicao = '1';
        }

        if ($condicao['valor'] == 'false')
        {
            $valorCondicao = '0';  
        }
        if (is_null($valorPai) || $valorPai == '')
        {
            return false;
        }

        switch ($operador) {
            case '==':
                $resultado = $valorPai == $valorCondicao;
                break;
            case '!=':
                $resultado = $valorPai != $valorCondicao;
                break;
            case '>':
                $resultado = $valorPai > $valorCondicao;
                break;
            case '<':
                $resultado = $valorPai < $valorCondicao;
                break;
            case '>=':
                $resultado = $valorPai >= $valorCondicao;
                break;
            case '<=':
                $resultado = $valorPai <= $valorCondicao;
                break;
            default:
                $resultado = false;
        }

        if (isset($condicao['composicao'], $condicao['condicao'])) {
            $composicao = $condicao['composicao'];
            $subCondicao = $condicao['condicao'];

            $subResultado = $this->avaliarCondicao($valorPai, $subCondicao);

            if ($composicao === '||') {
                $resultado = $resultado || $subResultado;
            } elseif ($composicao === '&&') {
                $resultado = $resultado && $subResultado;
            }
        }

        return $resultado;
    }

    public function verificar_atributos_default($atributo, $id_item, $grupo_tarifario_item )
    {
        // adiciona atributos nivel 1 se não existirem
        if (empty($id_item) || empty($atributo) || empty($grupo_tarifario_item)) 
        {
            return;
        }

        $this->db->select("attr.atributo, attr.atributo_pai, attr.codigo");
        $this->db->where('ci.id_item', $id_item);
        $this->db->join('cad_item ci', 'ci.id_item = attr.id_item', 'inner');
        $query = $this->db->get('cad_item_attr attr');
        $attrs_item =  $query->result();

        $attr_db = [];
        if (!empty($attrs_item))
        {
            foreach ($attrs_item as $attr)
            {
                $attr_db[] = $attr->atributo;
            }
        }
 
        foreach ($atributo as $attr)
        {
            if (empty($attr_db) || !in_array($attr['codigo'],$attr_db))
            {

                $descricao = "";
                if (isset($attr["dominio"])) {
                    foreach ($attr["dominio"] as $dominio) {
                        if ($dominio["codigo"] == $attr['codigo']) {
                            $descricao = $dominio["descricao"];
                            break;
                        }
                    }
                }
                
                $dbdata = array(
                    'codigo' => '',
                    'apresentacao' => $attr['nomeApresentacao'],
                    'descricao' => $descricao,
                    'atributo_pai' => '',
                    'atributo' => $attr['codigo'],
                    'obrigatorio' => $attr['obrigatorio'],
                    'id_item' => $id_item,
                    'id_grupo_tarifario' => $grupo_tarifario_item,
                    'id_usuario' => 1,
                    'modalidade' => $attr['modalidade'],
                    'criado_em' =>  \date("Y-m-d H:i:s"),
                    'atualizado_em' => \date("Y-m-d H:i:s"),
                    'ativo' => 1
                );
            
                $this->db->insert('cad_item_attr', $dbdata);
            }
        }
    }
    public function get_grp_tarif_item($id_item)
    {
        if (empty($id_item))
        {
            return;
        }

        $this->db->select('id_grupo_tarifario');
        $this->db->where('id_item', $id_item);
        return $this->db->get('cad_item')->row()->id_grupo_tarifario;
    }

    public function limpar_registros_grupo_tarif_divergente($id_item, $grupo_tarifario_item)
    {
        if (empty($id_item) || empty($grupo_tarifario_item))
        {
            echo 'id_item ou grupo_tarifario_item vazio';
            return;
        }

        $this->db->select('*');
        $this->db->from('cad_item_attr');
        $this->db->where('id_item', $id_item);
        $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $rows = $query->result_array();
            $rows_object = $query->result();

            foreach ($rows_object as $r)
            {
                // grupo tarifario que esta divergente na cad_item_attr em relacao a cad_item mas que é do mesmo id_item
                $this->db->where('id_grupo_tarifario', $r->id_grupo_tarifario);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;
    
                // grupo_tarifario na cad_item
                $this->db->where('id_grupo_tarifario', $grupo_tarifario_item);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;
    
                if (!empty($r) && !empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario)
                {
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->delete('cad_item_attr');

                    $logs_delete = [
                        'id' => $r->id,
                        'codigo' => $r->codigo,
                        'apresentacao' => $r->apresentacao,
                        'descricao' => $r->descricao,
                        'atributo_pai' => $r->atributo_pai,
                        'atributo' => $r->atributo,
                        'obrigatorio' => $r->obrigatorio,
                        'id_item' => $r->id_item,
                        'id_grupo_tarifario' => $r->id_grupo_tarifario,
                        'id_usuario' => $r->id_usuario,
                        'modalidade' => $r->modalidade,
                        'criado_em' => $r->criado_em,
                        'atualizado_em' => $r->atualizado_em,
                        'ativo' => $r->ativo,
                        'tenant_id' => $r->tenant_id
                    ];                    
 
                   // $this->db->insert('logs_delete_cad_item_attr', $logs_delete);
                    
                } else {
                    $this->db->set('id_grupo_tarifario', $grupo_tarifario_item);
                    $this->db->from('cad_item_attr');
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->update('cad_item_attr attr');                    
                }

            }

        }
    }

    public function processarAtributosCondicionados($atributos, $attr_salvo_db, &$attr_default_item)
    {
        foreach ($atributos as $atributo) {
     
            // Verifica se o atributo é condicionante
            if (isset($atributo['atributoCondicionante']) && $atributo['atributoCondicionante'] == 1) {
                if (isset($atributo['condicionados']) && is_array($atributo['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }

            // Processa dbdata dentro de atributo
            if (!empty($atributo['atributo']['dbdata']['codigo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                    $attrPai = $matches[1];

                    if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'])) {
                        if (!in_array($atributo['atributo']['codigo'], $attr_default_item))
                        {
                            $attr_default_item[] = $atributo['atributo']['codigo'];

                            $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                'codigo' => '',
                                'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                'descricao' => '',
                                'atributo_pai' => isset($atributo['atributo']['codigo_pai']) ? $atributo['atributo']['codigo_pai'] : '',
                                'atributo' => $atributo['atributo']['codigo'],
                                'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,                                'id_usuario' => 1,
                                'modalidade' => isset($atributo['atributo']['modalidade']) ? $atributo['atributo']['modalidade'] : '',
                                'criado_em' =>  \date("Y-m-d H:i:s"),
                                'atualizado_em' => \date("Y-m-d H:i:s"),
                                'ativo' => 1
                            ];
                        }
                        
                    }
                }
            
            }  else if (isset($atributo['atributo']) && !empty($atributo['atributo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                        $attrPai = $matches[1];
                        if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'] )) {
                            if (!in_array($atributo['atributo']['codigo'], $attr_default_item))
                            {
                                $attr_default_item[] = $atributo['atributo']['codigo'];
                                $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                    'codigo' => '',
                                    'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                    'descricao' => '',
                                    'atributo_pai' => isset($atributo['atributo']['codigo_pai']) ? $atributo['atributo']['codigo_pai'] : '',
                                    'atributo' => $atributo['atributo']['codigo'],
                                    'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,
                                    'id_usuario' => 1,
                                    'modalidade' =>  isset($atributo['atributo']['modalidade']) ? $atributo['atributo']['modalidade'] : '',
                                    'criado_em' =>  \date("Y-m-d H:i:s"),
                                    'atualizado_em' => \date("Y-m-d H:i:s"),
                                    'ativo' => 1
                                ];
                            }
                            
                        }
                    }  
                }
                
            // Processa dbdata no nível atual
            if (!empty($atributo['dbdata'])) {
                if (!in_array($atributo['codigo'], $attr_default_item))
                {
                    $attr_default_item[] = $atributo['codigo'];
                    $this->_attr_estrutura_completa[$atributo['codigo']] = [
                        'codigo' => '',
                        'apresentacao' => $atributo['nomeApresentacao'],
                        'descricao' => '',
                        'atributo_pai' => $atributo['codigo_pai'],
                        'atributo' => $atributo['codigo'],
                        'obrigatorio' => !empty($atributo['obrigatorio']) ? $atributo['obrigatorio'] : 0,
                        'id_usuario' => 1,
                        'modalidade' => $atributo['modalidade'],
                        'criado_em' =>  \date("Y-m-d H:i:s"),
                        'atualizado_em' => \date("Y-m-d H:i:s"),
                        'ativo' => 1
                    ];
                }
                
            }

            // Verifica níveis adicionais de condicionantes
            if (isset($atributo['atributo']) && isset($atributo['atributo']['atributoCondicionante']) && $atributo['atributo']['atributoCondicionante'] == 1) {
                if (isset($atributo['atributo']['condicionados']) && is_array($atributo['atributo']['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['atributo']['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }

            // Verifica níveis adicionais de condicionantes
            if (!empty($atributo['listaSubatributos']) ) {
                $this->processarAtributosCondicionados($atributo['listaSubatributos'], $attr_salvo_db, $attr_default_item);
            }

            // // Processa dbdata dentro de atributo
            // if (!empty($atributo['atributo']['dbdata'])) {

            //     $dbdata = $atributo['atributo']['dbdata'];
            //     if (isset($dbdata['atributo_pai']) && isset($atributo['condicao'])) {
            //         if ($this->avaliarCondicao($attr_salvo_db[$dbdata['atributo_pai']] ?? null, $atributo['condicao'], $dbdata['atributo_pai'])) {
            //             $attr_default_item[] = $atributo['atributo']['codigo'];
            //         }
            //     }
            // }
    
            // // Processa dbdata no nível atual
            // if (!empty($atributo['dbdata'])) {
            //     $attr_default_item[] = $atributo['codigo'];
            // }
    
            // // Verifica níveis adicionais de condicionantes
            // if (isset($atributo['atributo']) && isset($atributo['atributo']['atributoCondicionante']) && $atributo['atributo']['atributoCondicionante'] == 1) {
            //     if (isset($atributo['atributo']['condicionados']) && is_array($atributo['atributo']['condicionados'])) {
            //         $this->processarAtributosCondicionados($atributo['atributo']['condicionados'], $attr_salvo_db, $attr_default_item);
            //     }
            // }

            // // Verifica níveis adicionais de condicionantes
            // if (!empty($atributo['listaSubatributos']) ) {
            //     $this->processarAtributosCondicionados($atributo['listaSubatributos'], $attr_salvo_db, $attr_default_item);
            // }
        }

        return $attr_default_item;
    }

    public function limpar_atributos_nao_relacionados($ncm)
    {
        echo 'iniciando';
        $count = 0;
        $this->_virtual_table = [];

        $this->db->where("ativo", 1);
        //$this->db->where("id_empresa", 353);
        $this->db->order_by("id_empresa", "ASC");
        $query = $this->db->get('empresa');

        $empresas =  $query->result();
        foreach ($empresas as $empresa) {
            echo 'Analisando empresa ' . $empresa->nome_fantasia .' Ncm: '.$ncm.' '.$this->memory_mb(memory_get_usage()).PHP_EOL;
            $table_cad_item = $this->get_cad_item($empresa->id_empresa, $ncm);
            if ($table_cad_item->num_rows() == 0) {
                continue;
            }

            $count = 0;
            while ($table = $table_cad_item->unbuffered_row()) {
                $total = $table_cad_item->num_rows();
                $count++;
                echo 'Analisando item '.$count.' de '.$total.' id_item: '.$table->id_item.' empresa: '.$empresa->nome_fantasia.' '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        
                $grupo_tarifario_item = $this->get_grp_tarif_item($table->id_item);
                if (!empty($grupo_tarifario_item) && !empty($table->id_item))
                {
                    echo 'limpar_registros_grupo_tarif_divergente '.$grupo_tarifario_item. ' ';
                    $this->limpar_registros_grupo_tarif_divergente($table->id_item, $grupo_tarifario_item);
                }

                $this->_attrs = [];
                if (!empty($table->ncm_proposto))
                {
                    $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($table->ncm_proposto));
                    if (empty($ncm_item))
                    {
                        continue;
                    }
        
                    $array = $this->get_attr($table->id_item);  
                    if (empty($array))
                    {
                        continue;
                    }
 
                    if (empty($ncm_item["listaAtributos"]))
                    {
                        continue;
                    }
                    $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                    $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

                    $this->verificar_atributos_default($ncm_item["assocAttrs"], $table->id_item, $grupo_tarifario_item);

                
                    $atributos_ncm = $this->_attrs;
                    $attr_default_item = [];
                    $attr_salvo_db = [];
                    $resultado = $this->filtrarAtributosFilhos($atributos_ncm);
                    $atributos_com_pai = [];
                
                    if (!empty($atributos_ncm))
                    {
                        foreach ($atributos_ncm as $registro)
                        {
                            if (empty($registro[2]))
                            {
                                continue;
                            }
                            $atributos_com_pai[] = $registro[2];
                        }
                        if (!empty($atributos_com_pai))
                        {
                            $this->db->set('atributo_pai', null);
                            $this->db->from('cad_item_attr');
                            $this->db->where('id_item', $table->id_item);
                            $this->db->where_not_in('atributo', $atributos_com_pai);
                            $this->db->update('cad_item_attr attr');
                        }
        
                        foreach ($atributos_ncm as $registro)
                        {
                            // atributo filho [2] atributo pai  [0]
                            if (!empty($registro[0]) && !empty($registro[2]) && in_array($registro[2], $resultado)  )
                            {
                                    $this->db->query(" UPDATE cad_item_attr
                                    SET atributo_pai = '{$registro[0]}'
                                        WHERE id_item = '{$table->id_item}'
                                        AND atributo = '{$registro[2]}'
                                        AND (atributo_pai <> '{$registro[0]}' OR atributo_pai IS NULL)");
                            }
                        }
        
                        $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                        $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);
                    }

                    // Construindo os atributos salvos no banco de dados.
                    foreach ($ncm_item["defaultAttrs"] as $i) {
                        $attr_salvo_db[$i->atributo] = $i->codigo;
                    }

                    $attr_default_item = $this->processarAtributosCondicionados($ncm_item["assocAttrs"], $attr_salvo_db, $attr_default_item);

                    if (!empty($attr_salvo_db) && !empty($attr_default_item))
                    {
                        foreach ($attr_default_item as $attr)
                        {
                            if (!array_key_exists($attr, $attr_salvo_db)) 
                            {

                                if (!empty($this->_attr_estrutura_completa[$attr]))
                                {
                                    $item = $this->_attr_estrutura_completa[$attr];
                                    $item['id_item'] = $table->id_item;
                                    $item['id_grupo_tarifario'] = $table->id_grupo_tarifario;
                                    
                                    $this->db->replace('cad_item_attr', $item);
                                }
                            
                            }
                        }
                    }

                    if (!empty($attr_default_item) && !empty($table->id_item))
                    {
                    
                        $this->db->select('*');
                        $this->db->from('cad_item_attr');
                        $this->db->where('id_item', $table->id_item);
                        $this->db->where_not_in('atributo', $attr_default_item);
                       // $this->db->where_not_in('atributo_pai', $attr_default_item);
                        $query = $this->db->get();
        
                        if ($query->num_rows() > 0) {
                            $rows = $query->result_array();
                            // $this->db->insert_batch('logs_delete_cad_item_attr', $rows);
        
                            $this->db->where('id_item', $table->id_item);
                            $this->db->where_not_in('atributo', $attr_default_item);
                            $this->db->delete('cad_item_attr');
                        }
                    }

                }
 
                
            }
        }
    }
    
    public function memory_mb($mem)
    {
        return $mem / (1024 * 1024);
    }

    public function remove_attrs($data, $attrsPreenchidos, $attrsDefault, $delete)
    {
        if (!empty($data['attrs']) && !empty($data['id_item']) && !empty($data['ncm'])) {
            if ($delete) {
                $this->db->where('atributo_pai IS NOT NULL AND atributo_pai <> ""', NULL, FALSE);
                $this->db->where('id_item', $data['id_item']);
                $this->db->where_not_in('atributo', $data['attrs']);
                $this->db->delete('cad_item_attr');
            }

            $this->db->where('cad_item_attr.id_item', $data['id_item']);
            $this->db->join('cad_item', 'cad_item.id_item = cad_item_attr.id_item', 'inner');
            $query = $this->db->get('cad_item_attr');
            $item =  $query->result();

            $id_grupo_tarifaria = reset($item)->id_grupo_tarifario;

            foreach ($attrsPreenchidos as $atributo) {

                if ($query->num_rows() != count($attrsPreenchidos)) {


                    $found = FALSE;
                    foreach ($item as $i) {

                        if ($i->atributo == $atributo['codigo']) {
                            $found = TRUE;
                            // continue;
                        }
                    }

                    if (!$found) {

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['codigo']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $atributo['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo_pai'],
                            'atributo' => $atributo['codigo'],
                            'obrigatorio' => $atributo['obrigatorio'],
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }
                }

                $attrsDb = [];
                foreach ($item as $i) {
                    $attrsDb[] = $i->atributo;
                }

                foreach ($atributo['condicionados'] as $condicionado) {

                    if (
                        !empty($atributo['atributo_codigo_0'])
                        && isset($condicionado['atributo']['codigo'])
                        && $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_0']
                        && !in_array($atributo['atributo_codigo_0'], $attrsDb)
                    ) {
                        $cond_res = FALSE;
                        if (
                            $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_0'] &&
                            isset($atributo['dbdata']['atributo']) && $atributo['dbdata']['atributo'] ==  $atributo['codigo']
                        ) {
                            $attr_val = $atributo['dbdata']['codigo'];
                            $cond_op  = $condicionado['condicao']['operador'];
                            $cond_val = $condicionado['condicao']['valor'];
                            if ($cond_val == true) {
                                $cond_val = 1;
                            }

                            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
                        }

                        if (!$cond_res) {
                            continue;
                        }

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['atributo_codigo_0']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $condicionado['atributo']['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo'],
                            'atributo' => $atributo['atributo_codigo_0'],
                            'obrigatorio' => $atributo['atributo_obrigatorio_0'] == 'true' ? 1 : 0,
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }

                    if (
                        !empty($atributo['atributo_codigo_1'])
                        && isset($condicionado['atributo']['codigo'])
                        && $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_1']
                        && !in_array($atributo['atributo_codigo_1'], $attrsDb)
                    ) {
                        $cond_res = FALSE;
                        if (
                            $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_1'] &&
                            isset($atributo['dbdata']['atributo']) && $atributo['dbdata']['atributo'] ==  $atributo['codigo']
                        ) {
                            $attr_val = $atributo['dbdata']['codigo'];
                            $cond_op  = $condicionado['condicao']['operador'];
                            $cond_val = $condicionado['condicao']['valor'];
                            if ($cond_val == true) {
                                $cond_val = 1;
                            }

                            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
                        }

                        if (!$cond_res) {
                            continue;
                        }

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['atributo_codigo_1']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $condicionado['atributo']['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo'],
                            'atributo' => $atributo['atributo_codigo_1'],
                            'obrigatorio' => $atributo['atributo_obrigatorio_0'] == 'true' ? 1 : 0,
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }

                    if (
                        !empty($atributo['atributo_codigo_2'])
                        && isset($condicionado['atributo']['codigo'])
                        && $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_2']
                        && !in_array($atributo['atributo_codigo_2'], $attrsDb)
                    ) {
                        $cond_res = FALSE;
                        if (
                            $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_2'] &&
                            isset($atributo['dbdata']['atributo']) && $atributo['dbdata']['atributo'] ==  $atributo['codigo']
                        ) {
                            $attr_val = $atributo['dbdata']['codigo'];
                            $cond_op  = $condicionado['condicao']['operador'];
                            $cond_val = $condicionado['condicao']['valor'];
                            if ($cond_val == true) {
                                $cond_val = 1;
                            }

                            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
                        }

                        if (!$cond_res) {
                            continue;
                        }

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['atributo_codigo_2']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $condicionado['atributo']['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo'],
                            'atributo' => $atributo['atributo_codigo_2'],
                            'obrigatorio' => $atributo['atributo_obrigatorio_0'] == 'true' ? 1 : 0,
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }


                    if (
                        !empty($atributo['atributo_codigo_3'])
                        && isset($condicionado['atributo']['codigo'])
                        && $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_3']
                        && !in_array($atributo['atributo_codigo_3'], $attrsDb)
                    ) {
                        $cond_res = FALSE;
                        if (
                            $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_3'] &&
                            isset($atributo['dbdata']['atributo']) && $atributo['dbdata']['atributo'] ==  $atributo['codigo']
                        ) {
                            $attr_val = $atributo['dbdata']['codigo'];
                            $cond_op  = $condicionado['condicao']['operador'];
                            $cond_val = $condicionado['condicao']['valor'];
                            if ($cond_val == true) {
                                $cond_val = 1;
                            }

                            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
                        }

                        if (!$cond_res) {
                            continue;
                        }

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['atributo_codigo_3']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $condicionado['atributo']['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo'],
                            'atributo' => $atributo['atributo_codigo_3'],
                            'obrigatorio' => $atributo['atributo_obrigatorio_0'] == 'true' ? 1 : 0,
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }

                    if (
                        !empty($atributo['atributo_codigo_4'])
                        && isset($condicionado['atributo']['codigo'])
                        && $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_4']
                        && !in_array($atributo['atributo_codigo_4'], $attrsDb)
                    ) {
                        $cond_res = FALSE;
                        if (
                            $condicionado['atributo']['codigo'] == $atributo['atributo_codigo_4'] &&
                            isset($atributo['dbdata']['atributo']) && $atributo['dbdata']['atributo'] ==  $atributo['codigo']
                        ) {
                            $attr_val = $atributo['dbdata']['codigo'];
                            $cond_op  = $condicionado['condicao']['operador'];
                            $cond_val = $condicionado['condicao']['valor'];
                            if ($cond_val == true) {
                                $cond_val = 1;
                            }

                            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
                        }

                        if (!$cond_res) {
                            continue;
                        }

                        $descricao = "";
                        if (isset($atributo["dominio"])) {
                            foreach ($atributo["dominio"] as $dominio) {
                                if ($dominio["codigo"] == $atributo['atributo_codigo_4']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $condicionado['atributo']['nomeApresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo'],
                            'atributo' => $atributo['atributo_codigo_4'],
                            'obrigatorio' => $atributo['atributo_obrigatorio_0'] == 'true' ? 1 : 0,
                            'id_item' => $data['id_item'],
                            'id_grupo_tarifario' => $id_grupo_tarifaria,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );

                        $this->db->insert('cad_item_attr', $dbdata);
                    }
                }
            }

            if ($delete) {
                $this->db->where('atributo_pai IS NOT NULL AND atributo_pai <> ""', NULL, FALSE);
                $this->db->where('id_item', $data['id_item']);
                $this->db->where_not_in('atributo', $data['attrs']);
                $this->db->delete('cad_item_attr');
            }
        }
    }

    public function import()
    {
        $directory = FCPATH . 'assets/atributos-json/';
        $tempZipFile = $directory . 'atributos_temp.zip';

        // Verifique se a pasta tem permissão de escrita
        if (!is_writable($directory)) {
            $this->log("Erro: A pasta não tem permissões de escrita.");
            $this->_send_mail[] = $this->_code_mail['0'];
            return;
        }

        // Remover o arquivo atributos.json, caso exista
        $jsonFilePath = FCPATH . 'assets/atributos-json/atributos.json';
        if (file_exists($jsonFilePath)) {
            if (!unlink($jsonFilePath)) {
                $this->log("Erro: Não foi possível remover o arquivo atributos.json existente.");
                $this->_send_mail[] = $this->_code_mail['0'];
                return;
            } else {
                $this->log("Arquivo atributos.json removido com sucesso.");
            }
        }

        // Baixar o arquivo ZIP
        $fileContent = file_get_contents(config_item('link_file_json'));

        // Para fins de testes
        // $fileContent = "Este não é um arquivo ZIP válido!"; // Somente para testes de debug
        // $fileContent = file_get_contents('https://example.com/arquivo-corrompido.zip'); // Arquivo corrompido
        // $fileContent = ''; // Arquivo vazio
        
        echo config_item('link_file_json');
        sleep(2);
        if ($fileContent === false) {
            $this->log("Erro ao baixar o arquivo ZIP.");
            $this->_send_mail[] = $this->_code_mail['1'];
            return;
        }

        // Salvar o arquivo ZIP
        if (file_put_contents($tempZipFile, $fileContent) === false) {
            $this->log("Erro ao salvar o arquivo ZIP.");
            $this->_send_mail[] = $this->_code_mail['0'];
            return;
        }

        // Verificar se o arquivo ZIP foi baixado corretamente
        if (!file_exists($tempZipFile) || filesize($tempZipFile) == 0) {
            $this->log("Erro: O arquivo ZIP não foi baixado corretamente.");
            $this->_send_mail[] = $this->_code_mail['1'];
            return;
        }

        sleep(2);

        // Abrir o arquivo ZIP
        $zip = new ZipArchive;
        $result = $zip->open($tempZipFile);
        if ($result === true) {
            sleep(2);

            // Listar arquivos dentro do ZIP
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $stat = $zip->statIndex($i);
                $this->log('Arquivo dentro do ZIP: ' . basename($stat['name']));
            }

            // Extrair o arquivo ZIP
            if ($zip->extractTo($directory)) {
                $this->log("Arquivo extraído com sucesso.");

                // Chamar função para importar dados
                $zipFileName = $zip->getNameIndex(0);
                //$data = json_decode(file_get_contents($directory  .'ATRIBUTOS_POR_NCM_2025_08_22.json'));
                $data = json_decode(file_get_contents($directory . $zipFileName));

                if ($data && $data->versao > trim(file_get_contents($directory . 'data.txt'))) {
                    $result = $this->import_informacoes_ncm($zipFileName, $data);
                    $this->db->truncate('ncm_atributo');
                    $result_ncm_sem_atributo = $this->import_ncm_sem_atributos($data);
                    
                    if ($result && $result_ncm_sem_atributo) {
                        $this->log("Informações NCM importadas com sucesso.");
                        $result_ncm_atributo = $this->import_ncm_atributo($zipFileName, $data);
                        if ($result_ncm_atributo) {
                            $this->log("NCM Atributo importados com sucesso.");
                            file_put_contents($directory . 'data.txt', $data->versao);
                            $this->_send_mail[] = $this->_code_mail['10'];
                        } else {
                            $this->log("Erro ao importar NCM Atributo.");
                            $this->_send_mail[] = $this->_code_mail['5'];
                        }
                    } else {
                        $this->log("Erro ao importar informações NCM.");
                        $this->_send_mail[] = $this->_code_mail['4'];
                    }
                } else {
                    $this->log("A versão do arquivo zipado não é maior que a versão no txt.");
                    $this->_send_mail[] = $this->_code_mail['7'];
                }
            } else {
                $this->log("Erro ao extrair o arquivo.");
                $this->_send_mail[] = $this->_code_mail['3'];
            }

            $zip->close();
        } else {
            // Mapeamento de códigos de erro
            $errorCodes = [
                ZipArchive::ER_EXISTS   => "ER_EXISTS: Arquivo já existe",
                ZipArchive::ER_INCONS   => "ER_INCONS: ZIP inconsistente",
                ZipArchive::ER_INVAL    => "ER_INVAL: Argumento inválido",
                ZipArchive::ER_MEMORY   => "ER_MEMORY: Falta de memória",
                ZipArchive::ER_NOENT    => "ER_NOENT: Arquivo não encontrado",
                ZipArchive::ER_NOZIP    => "ER_NOZIP: Não é um arquivo ZIP",
                ZipArchive::ER_OPEN     => "ER_OPEN: Não foi possível abrir o arquivo",
                ZipArchive::ER_READ     => "ER_READ: Erro de leitura",
                ZipArchive::ER_SEEK     => "ER_SEEK: Erro de busca",
            ];
        
            // Mensagem padrão para códigos desconhecidos
            $errorMessage = isset($errorCodes[$result])
                ? $errorCodes[$result]
                : "Erro desconhecido (Código: $result)";
        
            // Atualiza a mensagem do código 2 com detalhes
            $this->_code_mail['2']['msg'] = sprintf(
                $this->_code_mail['2']['msg'],
                $errorMessage
            );
        
            $this->_send_mail[] = $this->_code_mail['2'];
            $this->log("Erro ao abrir ZIP: $errorMessage");
        }

        // Remover o arquivo ZIP temporário
        unlink($tempZipFile);
    }

    public function import_ncm_sem_atributos($data)
    {
        foreach ($data->listaNcm as $ncm) {
            $cod_ncm = $ncm->codigoNcm;

            if (empty($ncm->listaAtributos))
            {
                $this->db->insert('ncm_atributo', [
                    'ncm' => $this->clean($cod_ncm),
                    'codigo' => 'null'
                ]);
            }
        }

        return true;

    }

    public function import_informacoes_ncm($nome_arquivo, $data)
    {
        $this->db->truncate('informacoes_ncm');

        // $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/'.$nome_arquivo));

        foreach ($data->listaNcm as $ncm) {
            $cod_ncm = $ncm->codigoNcm;

            if (!empty($ncm->listaAtributos))
            {
                foreach ($ncm->listaAtributos as $attr) {
                    $db = array(
                        'codigo'             => $attr->codigo,
                        'modalidade'         => $attr->modalidade,
                        'obrigatorio'        => $attr->obrigatorio,
                        'multivalorado'      => $attr->multivalorado,
                        'dataInicioVigencia' => $attr->dataInicioVigencia,
                        'codigoNcm'          => $cod_ncm
                    );
    
                    $this->db->insert('informacoes_ncm', $db);
                    $this->log('Importando atributo: ' . $attr->codigo);
                }
            }
        }

        $total = $this->db->count_all_results('informacoes_ncm');
        if ($total > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function import_ncm_atributo($nome_arquivo, $data)
    {
        
        // $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/'.$nome_arquivo));

        foreach ($data->detalhesAtributos as $detalhes) {
            $this->db->where('codigo', $detalhes->codigo);
            $this->db->order_by('dataInicioVigencia', 'desc');
            $query = $this->db->get('informacoes_ncm');
            $dbNcms = $query->result();

            foreach ($dbNcms as $ncm) {
                unset($detalhes->dataInicioVigencia);
                $dataAttr = array_merge((array) $ncm, (array) $detalhes);

                $attr = (object) $dataAttr;

                $this->insert_data($ncm->codigoNcm, $attr);
                if (!empty($attr->listaSubatributos)) {
                    foreach ($attr->listaSubatributos as $subattr) {
                        $this->insert_data($ncm->codigoNcm, $subattr, $attr->codigo);
                    }
                }
            }
        }

        $total = $this->db->count_all_results('ncm_atributo');
        if ($total > 0) {
            return true;
        } else {
            return false;
        }
    }

    private function send_mail($data_mail)
    {
        $this->load->library('email');

        $ambiente = config_item('ambiente');

        $body = $this->load->view('templates/notificacao_import_json', ['data' => $data_mail], true);

        $this->email->from(config_item('mail_from_addr'));
        $this->email->to(config_item('email_file_json'));
        $this->email->cc('<EMAIL>');

        $this->email->subject(' [Gestão Tarifária] ' . $ambiente . ' - Atualização automatica de atributos ');
        $this->email->message($body);
        $this->email->send();
    }

    private function clean($text)
    {
        return str_replace([
            '.',
            '-',
            '/'
        ], '', $text);
    }

    private function can_import($attr, $codigo_pai = null)
    {
        if (!empty($codigo_pai))
            return true;

        $lista_codigos = array_pluck($attr->objetivos, 'codigo');

        if ($attr->modalidade == 'Importação' && in_array(7, $lista_codigos))
            return true;

        return false;
    }

    private function log($message)
    {
        echo '[' . date('Y-m-d H:i:s') . '] - ' . $message . PHP_EOL;
    }

    private function insert_data($ncm, $attr, $codigo_pai = null)
    {
        if ($this->can_import($attr, $codigo_pai)) {
            $this->db->insert('ncm_atributo', [
                'ncm' => $this->clean($ncm),
                'codigo' => $attr->codigo,
                'codigo_pai' => $codigo_pai,
                'nome_apresentacao' => $attr->nomeApresentacao,
                'forma_preenchimento' => $attr->formaPreenchimento,
                'orientacao_preenchimento' => !empty($attr->orientacaoPreenchimento) ? $attr->orientacaoPreenchimento : NULL,
                'tamanho_maximo' => !empty($attr->tamanhoMaximo) ? $attr->tamanhoMaximo : NULL,
                'modalidade' => !empty($attr->modalidade) ? $attr->modalidade : NULL,
                'definicao' => !empty($attr->definicao) ? $attr->definicao : NULL,
                'obrigatorio' => $attr->obrigatorio,
                'data_inicio_vigencia' => $attr->dataInicioVigencia,
                'data_fim_vigencia' => !empty($attr->dataFimVigencia) ? $attr->dataFimVigencia : NULL,
                'dominio' => !empty($attr->dominio) ? json_encode($attr->dominio) : NULL,
                'objetivos' => !empty($attr->objetivos) ? json_encode($attr->objetivos) : NULL,
                'orgaos' => !empty($attr->orgaos) ? json_encode($attr->orgaos) : NULL,
                'atributo_condicionante' => $attr->atributoCondicionante,
                'condicionados' => !empty($attr->condicionados) ? json_encode($attr->condicionados) : NULL,
                'multivalorado' => !empty($attr->multivalorado) ? $attr->multivalorado : NULL
            ]);

            $this->log('Importando atributo ncm_atributo: ' . $attr->codigo);
        }
    }

    public function import_data()
    {
        ini_set('memory_limit', '2048M');

        $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/atributos.json'));

        $this->db->truncate('ncm_atributo');
        $this->db->truncate('informacoes_ncm');

        foreach ($data->listaNcm as $ncm) {
            $cod_ncm = $ncm->codigoNcm;

            if (!empty($ncm->listaAtributos))
            {
                foreach ($ncm->listaAtributos as $attr) {
                    $db = array(
                        'codigo'             => $attr->codigo,
                        'modalidade'         => $attr->modalidade,
                        'obrigatorio'        => $attr->obrigatorio,
                        'multivalorado'      => $attr->multivalorado,
                        'dataInicioVigencia' => $attr->dataInicioVigencia,
                        'codigoNcm'                => $cod_ncm
                    );

                    $this->db->insert('informacoes_ncm', $db);
                }
            }
        }

        unset($data);
        $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/atributos.json'));

        foreach ($data->detalhesAtributos as $detalhes) {
            $this->db->where('codigo', $detalhes->codigo);
            $query = $this->db->get('informacoes_ncm');
            $dbNcms = $query->result();
            foreach ($dbNcms as $ncm) {
                $dataAttr = array_merge((array) $ncm, (array) $detalhes);
                $attr = (object) $dataAttr;

                $this->insert_data($ncm->codigoNcm, $attr);
                if (!empty($attr->listaSubatributos)) {
                    foreach ($attr->listaSubatributos as $subattr) {
                        $this->insert_data($ncm->codigoNcm, $subattr, $attr->codigo);
                    }
                }
            }
        }
    }
}
