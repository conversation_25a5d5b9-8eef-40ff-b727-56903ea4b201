<div class="modal fade" id="modal-atribuir-grupo-tarifario" tabindex="-1" role="dialog" aria-labelledby="modal-atribuir-grupo-tarifario-label" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document" style="width: 60%;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="modal-atribuir-grupo-tarifario-label">Atribuir Grupo Tarifário</h4>
            </div>
            <div class="modal-body">
                <div id="modal-atribuir-loading" style="text-align: center; padding: 40px; display: none;">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>Buscando grupos tarifários...</p>
                </div>
                <div id="modal-atribuir-content">
                    <ul class="nav nav-tabs" id="modalAtribuirTab" role="tablist">
                        <li class="nav-item <?php if (!$has_diana) echo 'active'; ?>">
                            <a class="nav-link" id="tab-atribuir-modal" data-toggle="tab" href="#atribuir-modal" role="tab" aria-controls="atribuir-modal" aria-selected="false">Atribuir</a>
                        </li>

                        <?php if ($has_diana) : ?>
                            <li class="nav-item">
                                <a class="nav-link <?php if ($has_diana) echo 'active'; ?>" id="tab-diana-modal" data-toggle="tab" href="#diana-modal" role="tab" aria-controls="diana-modal" aria-selected="true">Diana</a>
                            </li>
                        <?php endif; ?>
                    </ul>

                    <div class="tab-content" id="modalAtribuirTabContent">
                        <div class="tab-pane fade in active" id="atribuir-modal" role="tabpanel" aria-labelledby="tab-atribuir-modal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="text-right" style="height: 26px; line-height: 26px">
                                        <div id="response-ajax-modal" style="display: none;">
                                            <img src="<?php echo base_url('assets/img/ajax-loader.gif'); ?>" /> Aguarde, buscando dados...
                                        </div>
                                    </div>

                                    <div id="search_grupos_tarifarios_holder_modal">
                                        <form id="search_grupos_tarifarios_modal" action="<?php echo site_url('atribuir_grupo/ajax_get_grupos_tarifarios'); ?>">
                                            <label style="padding-right: 8px">Status:</label>
                                            <div class="input-group">
                                                <select class="form-control selectpicker"
                                                    id="filter_selected_modal" name="filter_selected"
                                                    title="Tipo">
                                                    <option value="id_grupo_tarifario">Por Grupo Tarifário</option>
                                                    <option value="caracteristica" selected>Por Característica</option>
                                                </select>
                                            </div>

                                            <div class="checkbox">
                                                <label><input type="checkbox" id="disable_filter_by_tag_modal" name="disable_filter_by_tag" value="1" />
                                                    Todos
                                                    <i class="glyphicon glyphicon-info-sign tooltip-atribuir tooltip-wide" data-toggle="tooltip" title="Utilize essa opção caso prefira pesquisar por todos os grupos tarifários, desabilitando o filtro automático por TAG."></i>
                                                </label>
                                            </div>
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="grupo_input" placeholder="Digite a descrição ou NCM do grupo tarifário" />
                                                <div class="input-group-btn">
                                                    <button type="submit" data-loading-text="..." class="btn btn-primary" id="send_search_grupos_tarifarios_modal"><i class="glyphicon glyphicon-search"></i></button>
                                                </div>
                                            </div>
                                            <input type="hidden" id="order_grupo_modal" name="ncm_recomendada" value="asc" />
                                        </form>
                                    </div>
                                    <div class="clearfix" style="margin-top: 10px;"></div>

                                    <!-- Container com overlay de carregamento -->
                                    <div id="grupos-container-modal" style="position: relative; min-height: 200px;">
                                        <!-- Overlay de carregamento melhorado -->
                                        <div id="loading-overlay-grupos-modal" style="display: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.9); z-index: 1000; border-radius: 4px;">
                                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                                <div style="margin-bottom: 15px;">
                                                    <i class="fa fa-spinner fa-spin fa-2x" style="color: #3276b1;"></i>
                                                </div>
                                                <div style="font-size: 16px; color: #666; font-weight: 500;">
                                                    Buscando grupos tarifários...
                                                </div>
                                                <div style="font-size: 13px; color: #999; margin-top: 5px;">
                                                    Aguarde um momento
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Lista de grupos -->
                                        <div class="panel-group" id="accordion_grupos_modal" role="tablist" aria-multiselectable="true" style="max-height: 50vh; overflow-y: auto;"></div>
                                    </div>

                                    <hr />
                                </div>
                            </div>
                        </div>

                        <?php if ($has_diana): ?>
                            <div class="tab-pane fade" id="diana-modal" role="tabpanel" aria-labelledby="tab-diana-modal">
                                <div id="atribuir-diana-app" class="col-md-12">
                                    <v-atribuir-diana></v-atribuir-diana>
                                </div>
                                <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/atribuir-diana.js?version=' . config_item('assets_version')) ?>"></script>
                                <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/atribuir-diana.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-confirmar-atribuicao-grupo-modal" <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : NULL; ?>>
                    Atribuir <i class="glyphicon glyphicon-ok"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<style>
    #modal-atribuir-grupo-tarifario .modal-body {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }

    /* Estilo para tooltip mais largo */
    .tooltip-wide+.tooltip .tooltip-inner {
        max-width: 400px !important;
        width: 400px !important;
        white-space: normal !important;
        text-align: left !important;
        padding: 10px 15px !important;
        line-height: 1.4 !important;
    }

    /* Alternativa usando seletor de atributo para maior especificidade */
    [data-toggle="tooltip"].tooltip-wide+.tooltip .tooltip-inner,
    .tooltip-wide[data-toggle="tooltip"]+.tooltip .tooltip-inner {
        max-width: 400px !important;
        width: 400px !important;
        white-space: normal !important;
        text-align: left !important;
        padding: 10px 15px !important;
        line-height: 1.4 !important;
    }

    /* Estilo global para tooltips gerados pela classe tooltip-wide */
    .tooltip.tooltip-wide-instance .tooltip-inner {
        max-width: 400px !important;
        width: 400px !important;
        white-space: normal !important;
        text-align: left !important;
        padding: 10px 15px !important;
        line-height: 1.4 !important;
    }
</style>

<script>
    $(document).ready(function() {
        // Função para inicializar tooltips personalizados
        function initCustomTooltips() {
            $('.tooltip-wide[data-toggle="tooltip"]').each(function() {
                var $element = $(this);

                // Destruir tooltip existente se houver
                if ($element.data('bs.tooltip')) {
                    $element.tooltip('destroy');
                }

                // Configurar novo tooltip com classe personalizada
                $element.tooltip({
                    html: true,
                    container: 'body',
                    placement: 'auto top',
                    trigger: 'hover',
                    template: '<div class="tooltip tooltip-wide-instance" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>'
                });
            });
        }

        // Inicializar tooltips quando o modal for mostrado
        $('#modal-atribuir-grupo-tarifario').on('shown.bs.modal', function() {
            setTimeout(initCustomTooltips, 100);
        });

        // Também inicializar imediatamente se o modal já estiver visível
        if ($('#modal-atribuir-grupo-tarifario').hasClass('in')) {
            initCustomTooltips();
        }

        // Funções para controlar o overlay de carregamento melhorado
        window.showLoadingOverlayGrupos = function() {
            $('#loading-overlay-grupos-modal').fadeIn(200);
            // Manter a mensagem original também para compatibilidade
            $("#response-ajax-modal").show();
        };

        window.hideLoadingOverlayGrupos = function() {
            $('#loading-overlay-grupos-modal').fadeOut(200);
            // Ocultar a mensagem original também
            $("#response-ajax-modal").hide();
        };

        // Interceptar o evento de submit do formulário de busca para mostrar o overlay
        $(document).on('submit', '#search_grupos_tarifarios_modal', function() {
            // Pequeno delay para permitir que o JavaScript original execute primeiro
            setTimeout(function() {
                showLoadingOverlayGrupos();
            }, 50);
        });

        // Interceptar quando o modal é aberto para mostrar o overlay inicial
        $('#modal-atribuir-grupo-tarifario').on('shown.bs.modal', function() {
            // Se não há grupos carregados, mostrar o overlay
            if ($('#accordion_grupos_modal').is(':empty') || $('#accordion_grupos_modal').html().trim() === '') {
                showLoadingOverlayGrupos();
            }
        });
    });
</script>