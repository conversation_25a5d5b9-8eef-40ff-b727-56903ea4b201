<template>
    <div id="diana-atribuir-grupo" class="row">
        <loading 
            :active.sync="isLoading" 
            :is-full-page="fullPage"
        >
        </loading>
        
        <table class="table table-striped table-hover" border="0" style="margin-top: 14px;">
            <thead>
                <tr>
                    <th width="1%">
                    </th>
                    <th width="39%">
                        <h3 class="title" style="margin: 10px 0px 10px -25px;">
                            NCM's 
                            <small>({{ Number(ncmBench).toFixed(2) }} segundos)</small>    
                        </h3>
                    </th>
                    <th width="60%">
                        <h3 class="title" style="margin: 10px 0px">
                            Grupo tarifário 
                            <small>({{ Number(grtBench).toFixed(2) }} segundos)</small>    
                        </h3>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in items" 
                    :key="index">
                    <td class="vertical-middle" @click="handleRadio('.radio-' + index)">
                        <input 
                            type="radio" 
                            name="grupo[]" 
                            id="grupo_selected" 
                            @click="handleClick(item.grupo_tarifario ? item.grupo_tarifario : item.ncm)"
                            data-from="predicao"
                            :class="'form-check-input ' + 'radio-' + index" 
                            :data-id-predicao-ordem="'predicao_ordem_' + (index+1)"
                            :value="item.grupo_tarifario != undefined ? item.grupo_tarifario.grupo_tarifario ? item.grupo_tarifario.grupo_tarifario.id_grupo_tarifario : '' : ''"
                        >

                        <input :id="'predicao_ordem_' + (index+1)" type="hidden" name="predicao_ordem" :value="index+1">
                        <input :id="'has_predicao_' + (index+1)" type="hidden" name="has_predicao" value="true">
                    </td>
                    <td class="vertical-middle">
                        <p class="percentage"><small>({{item.ncm ? formatDouble(item.ncm.acc) : 0}})% de acurácia</small></p>
                        <a class="info_gt" :data-attr-id-grupo-tarifario="item.ncm ? item.ncm.pred : ''">
                            <i class="glyphicon glyphicon-info-sign"></i>&nbsp;{{item.ncm ? item.ncm.pred : ''}}
                        </a>
                    </td>
                    <td>
                        <div class ="row align-center">
                            <div class="col">
                                <p class="percentage">
                                    <small>({{item.grupo_tarifario ? formatDouble(item.grupo_tarifario.acc) : 0}})% de acurácia</small>
                                </p>

                                <a class="ncm_info" :data-attr-ncm="item.grupo_tarifario != undefined ? item.grupo_tarifario.grupo_tarifario ? item.grupo_tarifario.grupo_tarifario.ncm_recomendada : '' : ''">
                                    <i class="glyphicon glyphicon-info-sign"></i>  
                                    {{item.grupo_tarifario != undefined ? item.grupo_tarifario.grupo_tarifario ? item.grupo_tarifario.grupo_tarifario.ncm_recomendada : '-' : '-'}}
                                </a>

                                <a class="info_gt" :data-attr-id-grupo-tarifario="item.grupo_tarifario != undefined ? item.grupo_tarifario.grupo_tarifario ? item.grupo_tarifario.grupo_tarifario.id_grupo_tarifario : '' : ''">
                                    <i class="glyphicon glyphicon-info-sign"></i>
                                    &nbsp;Detalhes
                                </a>
                                
                                <div class="desc-content"></div>
                                <div class="table-content"></div>
                                
                                <p>{{item.grupo_tarifario != undefined ? item.grupo_tarifario.grupo_tarifario ? item.grupo_tarifario.grupo_tarifario.descricao : item.grupo_tarifario.pred : ''}}</p>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import _ from 'lodash';

export default {
    data() {
        return {
            isLoading: false,
            fullPage: true,
            items: [],
            ncmBench: 0,
            grtBench: 0,
            descricao_item_part_number: false,
            part_number: ''
        }
    },
    props: {
    },
    methods: {
        handleClick({grupo_tarifario}) {
            if (grupo_tarifario) {
                $("#memoria_classificacao").val(grupo_tarifario.memoria_classificacao);
                $("#subsidio").val(grupo_tarifario.subsidio);
                // $("#caracteristica").val(grupo_tarifario.caracteristica);
            }
        },
        formatDouble(val) {
            if (_.isEmpty(val)) {
                return 0;
            }

            return _.round(val * 100, 2);
        },
        setItems(predicoes, grupoTarifario = true) {
            console.log(this.items);
            if (_.isEmpty(this.items)) {
                for (let i = 0; i < 3; i++) {
                    this.items.push({grupo_tarifario: {}, ncm: {}});
                }    
            }
            for (let i = 0; i < 3; i++) {
                if (grupoTarifario && this.items[i].grupo_tarifario !== undefined) {
                    this.items[i].grupo_tarifario = predicoes[i];
                } else {
                    this.items[i].ncm = predicoes[i];
                }
            }
        },
        async getGrupoTarifarios() {
            var self = this;
            await this.$http.post('consulta_diana/get_predictions/', {
                app: 'a',
                descricao_item_part_number: self.descricao_item_part_number,
                part_number: self.part_number
            }).then(response => {
                self.grtBench = response.data.result_benchmark;
                if (response.data.result) {
                    self.setItems(response.data.result.data[0].predictions);
                }
            })

            await this.$http.post('consulta_diana/get_predictions/', {
                app: 'b',
                descricao_item_part_number: self.descricao_item_part_number,
                part_number: self.part_number
            }).then(response => {
                self.isLoading = false;
                self.ncmBench = response.data.result_benchmark;
                if (response.data.result) {
                    self.setItems(response.data.result.data[0].predictions, false);
                }
            })
        },
        selectedPartnumber() {
            this.isLoading = true;
            this.getGrupoTarifarios();
        },
        handleRadio(className) {
            $('.form-check-input').prop('checked', false);
            $(className).prop('checked', true);

            var checkedRadio = $('input[type="radio"][name="grupo[]"]:checked').val();
            if (_.isEmpty(checkedRadio)) {
                swal("Atenção", "Não foi possível atribuir grupo! A predição selecionada não possui grupo tarifário relacionado.", "warning");
                return false;
            }
        },
        handleMultipleCheckboxes() {
            let self = this;

            if ($(".item_selected:checked").length >= 1) {
                let descItemPartNumber = null;
                let partNumber = null;

                // Pegar o primeiro item selecionado para fazer a predição
                $(".item_selected:checked").each(function() {
                    descItemPartNumber = $(this).attr('data-descricao');
                    partNumber = $(this).val();
                    return false; // Para no primeiro item
                });

                // Verificar se os dados mudaram para evitar chamadas desnecessárias
                if (descItemPartNumber == self.descricao_item_part_number && partNumber == self.part_number) {
                    return true;
                }

                // Validar se temos dados válidos
                if (!descItemPartNumber || !partNumber) {
                    console.warn('Diana: Dados insuficientes para predição', {
                        descItemPartNumber,
                        partNumber
                    });
                    return false;
                }

                self.descricao_item_part_number = descItemPartNumber;
                self.part_number = partNumber;

                console.log('Diana: Iniciando predição para', {
                    partNumber: self.part_number,
                    descricao: self.descricao_item_part_number,
                    totalSelecionados: $(".item_selected:checked").length
                });

                self.selectedPartnumber();
            }
        }
    },
    async mounted() {
        let self = this;

        // Administração de clicks únicos/múltiplos nos selectboxes de PART NUMBER (lado esquerdo)

        // Listener para o checkbox "selecionar todos" da interface antiga
        $('body').on('click', '#select-all', function() {
            setTimeout(function() {
                self.handleMultipleCheckboxes();
            }, 1500);
        })

        // Listener para o checkbox "selecionar todos" da nova interface
        $('body').on('click', '#select-all-checkbox', function() {
            setTimeout(function() {
                self.handleMultipleCheckboxes();
            }, 300);
        })

        // Listener para checkboxes individuais
        $('body').on('click', '.item_selected', function() {
            self.handleMultipleCheckboxes();
        });

        // Listener para evento customizado disparado pelo bulk_selection_footer.js
        document.addEventListener('selectAllItems', function() {
            setTimeout(function() {
                self.handleMultipleCheckboxes();
            }, 300);
        });

        // Administração de clicks nas tabs de atribuição

        $('#tab-atribuir, #tab-diana').on('click', function() {
            $('.form-check-input').prop('checked', false);
        });

        // Administração de clicks no link de NCM da listagem da predição

        $("body").on("click", '.ncm_info', function () {
            var ncm = $(this).attr("data-attr-ncm");

            if (_.isEmpty(ncm)) {
                swal("Atenção", "Não há NCM.", "warning");
                return false;
            }

            self.isLoading = true;
                
            if ($(this).parent().find('.table-content').html() == '') {
                $('.table-content, .desc-content').html('');
              
                self.$http.post('atribuir_grupo/ajax_get_ncm_info', {
                    'ncm': ncm
                }).then(({data}) => {
                    self.isLoading = false;
                    $(this).parent().find('.table-content').html(data + '<hr>');
                })
            } else {
                $(this).parent().find('.table-content').html('');	
                self.isLoading = false;
            }
        });

        // Administração de clicks no link de Detalhes da listagem da predição

        $('body').on("click", ".info_gt", function() {
            var id_grupo_tarifario = $(this).attr("data-attr-id-grupo-tarifario");
            var html = "";

            if (_.isEmpty(id_grupo_tarifario)) {
                swal("Atenção", "Não há ID do grupo tarifário.", "warning");

                return false;
            }

            self.isLoading = true;

            if ($(this).parent().find('.desc-content').html() == '') {
                $('.table-content, .desc-content').html('');
                
                self.$http.post('consulta_diana/ajax_get_info_grupo_tarifario', {
                    'id_grupo_tarifario': id_grupo_tarifario
                }).then(({data}) => {
                    self.isLoading = false;

                    if (data.observacao) {
                        html += "<b>Observacao</b>: " + data.observacao;
                    }

                    if (data.observacao && data.subsidio) {
                        html += "<br><br>";
                    }

                    if (data.subsidio) {
                        html += "<b>Subsídio</b>: " + data.subsidio
                    }

                    if (data.observacao || data.subsidio) {
                        html += "<hr>";
                    }

                    if (html == '') {
                        html = 'Não foi encontrada nenhuma informação.';
                    }

                    $(this).parent().find('.desc-content').html(html);
                })
            } else {
                $(this).parent().find('.desc-content').html('');
                self.isLoading = false;
            }
        });
    },
    components: {
        Loading
    },
}
</script>

<style type="text/css">
    .align-center {
        display: flex;
        margin-right: 10px;
        align-items: center;
    }
    .form-check-input:hover {
        cursor: pointer;   
    }
    .padding-none {
        padding: 0px !important;
    }
    .vertical-middle {
        vertical-align: middle !important;
        text-align: center;
    }
    #diana-atribuir-grupo {
        max-height: 75vh;
        overflow-y: auto;
    }
</style>
